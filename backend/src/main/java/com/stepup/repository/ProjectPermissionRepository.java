package com.stepup.repository;

import com.stepup.model.project.ProjectPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProjectPermissionRepository extends JpaRepository<ProjectPermission, String> {

    /**
     * Tìm permission của một user trên một project cụ thể
     */
    Optional<ProjectPermission> findByProjectIdAndUserId(String projectId, String userId);

    /**
     * L<PERSON>y tất cả permissions của một project
     */
    List<ProjectPermission> findByProjectId(String projectId);

    /**
     * Lấy tất cả projects mà user có permission
     */
    List<ProjectPermission> findByUserId(String userId);

    /**
     * <PERSON><PERSON><PERSON> tra user có permission trên project không
     */
    boolean existsByProjectIdAndUserId(String projectId, String userId);

    /**
     * Xóa permission của user trên project
     */
    void deleteByProjectIdAndUserId(String projectId, String userId);

    /**
     * Lấy danh sách user có permission trên project cùng với thông tin user
     */
    @Query("SELECT pp FROM ProjectPermission pp " +
            "WHERE pp.projectId = :projectId")
    List<ProjectPermission> findProjectPermissionsWithUsers(@Param("projectId") String projectId);
} 