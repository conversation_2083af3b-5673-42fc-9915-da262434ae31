package com.stepup.repository;

import com.stepup.model.checklist.ChecklistTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ChecklistTemplateRepository extends JpaRepository<ChecklistTemplate, String> {
    Optional<ChecklistTemplate> findByFeatureIdAndIsActiveTrue(String featureId);

    @Query("SELECT ct.data FROM ChecklistTemplate ct WHERE ct.featureId = :featureId AND ct.isActive = true")
    String getChecklistDataByFeatureId(String featureId);
}