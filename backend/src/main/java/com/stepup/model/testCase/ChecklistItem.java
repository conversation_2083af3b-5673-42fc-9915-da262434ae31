package com.stepup.model.testCase;

/**
 * Class để represent một checklist item vớ<PERSON> số thứ tự [n]
 */
public class ChecklistItem {
    private final String number;
    private final String text;
    
    public ChecklistItem(String number, String text) {
        this.number = number;
        this.text = text;
    }
    
    public String getNumber() {
        return number;
    }
    
    public String getText() {
        return text;
    }
    
    @Override
    public String toString() {
        return "ChecklistItem{" +
                "number='" + number + '\'' +
                ", text='" + text.substring(0, Math.min(50, text.length())) + "...'" +
                '}';
    }
} 