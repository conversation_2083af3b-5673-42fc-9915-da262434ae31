package com.stepup.model.testCase;

/**
 * Class để represent một markdown section
 */
public class MarkdownSection {
    private final String title;
    private final int level;
    private final String content;
    
    public MarkdownSection(String title, int level, String content) {
        this.title = title;
        this.level = level;
        this.content = content;
    }
    
    public String getTitle() {
        return title;
    }
    
    public int getLevel() {
        return level;
    }
    
    public String getContent() {
        return content;
    }
    
    @Override
    public String toString() {
        return "MarkdownSection{" +
                "title='" + title + '\'' +
                ", level=" + level +
                ", contentLength=" + content.length() +
                '}';
    }
} 