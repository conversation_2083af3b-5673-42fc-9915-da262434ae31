package com.stepup.model.testCase;

import java.util.List;

/**
 * Class để represent kết quả xử lý một category
 */
public class CategoryResult {
    private final String originalCategoryText;
    private final List<ChecklistItem> itemsInCategory;
    private final String categoryResponse;
    
    public CategoryResult(String originalCategoryText, List<ChecklistItem> itemsInCategory, String categoryResponse) {
        this.originalCategoryText = originalCategoryText;
        this.itemsInCategory = itemsInCategory;
        this.categoryResponse = categoryResponse;
    }
    
    public String getOriginalCategoryText() {
        return originalCategoryText;
    }
    
    public List<ChecklistItem> getItemsInCategory() {
        return itemsInCategory;
    }
    
    public String getCategoryResponse() {
        return categoryResponse;
    }
    
    @Override
    public String toString() {
        return "CategoryResult{" +
                "itemsCount=" + itemsInCategory.size() +
                ", responseLength=" + categoryResponse.length() +
                '}';
    }
} 