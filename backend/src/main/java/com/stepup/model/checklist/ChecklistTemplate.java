package com.stepup.model.checklist;

import com.stepup.model.ModifierTrackingEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "checklist_templates", indexes = {
        @Index(name = "checklist_templates_feature_id_version", columnList = "feature_id, version", unique = true),
        @Index(name = "checklist_templates_idx_active", columnList = "is_active")
})
public class ChecklistTemplate extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "feature_id")
    private String featureId;

    @Column(name = "version", length = 50)
    private String version = "1.0";

    @Column(name = "data", columnDefinition = "TEXT")
    private String data;

    @NotNull
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "created_by")
    private String createdBy;
}