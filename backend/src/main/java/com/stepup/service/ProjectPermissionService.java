package com.stepup.service;

import com.stepup.model.User;
import com.stepup.model.project.Project;
import com.stepup.model.project.ProjectPermission;
import com.stepup.repository.ProjectPermissionRepository;
import com.stepup.repository.ProjectRepository;
import com.stepup.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ProjectPermissionService extends CommonService {
    private final ProjectPermissionRepository projectPermissionRepository;
    private final UserRepository userRepository;
    private final ProjectRepository projectRepository;

    @Autowired
    public ProjectPermissionService(ProjectPermissionRepository projectPermissionRepository,
                                    UserRepository userRepository,
                                    ProjectRepository projectRepository) {
        this.projectPermissionRepository = projectPermissionRepository;
        this.userRepository = userRepository;
        this.projectRepository = projectRepository;
    }

    /**
     * Share project với user qua email
     */
    @Transactional
    public ProjectPermission shareProject(String projectId, String userEmail,
                                          ProjectPermission.PermissionType permissionType) {
        // 1. Kiểm tra project tồn tại
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("Project not found with id: " + projectId));

        // 2. Kiểm tra user hiện tại có quyền share không (phải là owner hoặc có quyền EDIT)
        User currentUser = retrieveUserFromRequest();
        if (!canManagePermissions(projectId, currentUser.getId())) {
            throw new RuntimeException("You don't have permission to share this project");
        }

        // 3. Tìm user được share
        User targetUser = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new RuntimeException("User not found with email: " + userEmail));

        // 4. Kiểm tra xem đã share chưa
        Optional<ProjectPermission> existingPermission =
                projectPermissionRepository.findByProjectIdAndUserId(projectId, targetUser.getId());

        if (existingPermission.isPresent()) {
            // Update existing permission
            ProjectPermission permission = existingPermission.get();
            permission.setPermissionType(permissionType);
            return projectPermissionRepository.save(permission);
        } else {
            // Create new permission
            ProjectPermission newPermission = ProjectPermission.builder()
                    .projectId(projectId)
                    .userId(targetUser.getId())
                    .permissionType(permissionType)
                    .build();
            return projectPermissionRepository.save(newPermission);
        }
    }

    /**
     * Kiểm tra user có quyền cụ thể trên project không
     */
    public boolean hasPermission(String projectId, String userId, ProjectPermission.PermissionType requiredPermission) {
        // 1. Kiểm tra owner
        Optional<Project> projectOpt = projectRepository.findById(projectId);
        if (projectOpt.isPresent() && projectOpt.get().getCreatedBy().equals(userId)) {
            return true; // Owner có mọi quyền
        }

        // 2. Kiểm tra permission trong bảng permissions
        Optional<ProjectPermission> permission =
                projectPermissionRepository.findByProjectIdAndUserId(projectId, userId);

        if (permission.isEmpty()) {
            return false; // Không có quyền
        }

        ProjectPermission.PermissionType userPermission = permission.get().getPermissionType();

        // 3. Logic kiểm tra: EDIT bao gồm VIEW, VIEW chỉ có VIEW
        if (requiredPermission == ProjectPermission.PermissionType.VIEW) {
            return userPermission == ProjectPermission.PermissionType.VIEW ||
                    userPermission == ProjectPermission.PermissionType.EDIT;
        } else if (requiredPermission == ProjectPermission.PermissionType.EDIT) {
            return userPermission == ProjectPermission.PermissionType.EDIT;
        }

        return false;
    }

    /**
     * Kiểm tra user có thể quản lý permissions không (owner hoặc có quyền EDIT)
     */
    public boolean canManagePermissions(String projectId, String userId) {
        // Owner luôn có quyền manage
        Optional<Project> projectOpt = projectRepository.findById(projectId);
        if (projectOpt.isPresent() && projectOpt.get().getCreatedBy().equals(userId)) {
            return true;
        }

        // Hoặc có quyền EDIT
        return hasPermission(projectId, userId, ProjectPermission.PermissionType.EDIT);
    }

    /**
     * Lấy danh sách permissions của project
     */
    @PreAuthorize("@securityService.canManageProjectPermissions(#projectId, null)")
    public List<ProjectPermission> getProjectPermissions(String projectId) {
        User currentUser = retrieveUserFromRequest();

        // Chỉ owner hoặc người có quyền EDIT mới xem được danh sách
        if (!canManagePermissions(projectId, currentUser.getId())) {
            throw new RuntimeException("You don't have permission to view project permissions");
        }

        return projectPermissionRepository.findByProjectId(projectId);
    }

    /**
     * Cập nhật permission của user
     */
    @Transactional
    public ProjectPermission updatePermission(String projectId, String userId,
                                              ProjectPermission.PermissionType newPermissionType) {
        User currentUser = retrieveUserFromRequest();

        // Kiểm tra quyền quản lý
        if (!canManagePermissions(projectId, currentUser.getId())) {
            throw new RuntimeException("You don't have permission to update permissions");
        }

        ProjectPermission permission = projectPermissionRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> new RuntimeException("Permission not found"));

        permission.setPermissionType(newPermissionType);
        return projectPermissionRepository.save(permission);
    }

    /**
     * Xóa permission (thu hồi quyền)
     */
    @Transactional
    public void removePermission(String projectId, String userId) {
        User currentUser = retrieveUserFromRequest();

        // Kiểm tra quyền quản lý
        if (!canManagePermissions(projectId, currentUser.getId())) {
            throw new RuntimeException("You don't have permission to remove permissions");
        }

        // Không thể xóa quyền của owner
        Optional<Project> projectOpt = projectRepository.findById(projectId);
        if (projectOpt.isPresent() && projectOpt.get().getCreatedBy().equals(userId)) {
            throw new RuntimeException("Cannot remove owner's permissions");
        }

        projectPermissionRepository.deleteByProjectIdAndUserId(projectId, userId);
    }

    /**
     * Lấy danh sách projects mà user có quyền truy cập
     */
    public List<ProjectPermission> getUserAccessibleProjects(String userId) {
        return projectPermissionRepository.findByUserId(userId);
    }

    /**
     * Lấy danh sách users được share trong project kèm thông tin chi tiết
     */
    @PreAuthorize("@securityService.canViewProject(#projectId)")
    public List<ProjectPermissionWithUserInfo> getProjectPermissionsWithUserInfo(String projectId) {
        User currentUser = retrieveUserFromRequest();

        // Lấy thông tin project để có owner
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("Project not found with id: " + projectId));

        List<ProjectPermission> permissions = projectPermissionRepository.findByProjectId(projectId);
        List<ProjectPermissionWithUserInfo> result = new ArrayList<>();

        // Thêm owner vào danh sách
        Optional<User> ownerOpt = userRepository.findById(project.getCreatedBy());
        if (ownerOpt.isPresent()) {
            User owner = ownerOpt.get();
            ProjectPermissionWithUserInfo ownerInfo = new ProjectPermissionWithUserInfo();
            ownerInfo.setPermissionId("owner-" + project.getCreatedBy());
            ownerInfo.setUserId(owner.getId());
            ownerInfo.setUserEmail(owner.getEmail());
            ownerInfo.setUserName(owner.getName());
            ownerInfo.setPermissionType("OWNER");
            ownerInfo.setCreatedAt(project.getCreatedAt());
            ownerInfo.setUpdatedAt(project.getUpdatedAt());
            result.add(ownerInfo);
        }

        // Thêm các users được share
        for (ProjectPermission permission : permissions) {
            Optional<User> userOpt = userRepository.findById(permission.getUserId());
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                ProjectPermissionWithUserInfo permissionInfo = new ProjectPermissionWithUserInfo();
                permissionInfo.setPermissionId(permission.getId());
                permissionInfo.setUserId(user.getId());
                permissionInfo.setUserEmail(user.getEmail());
                permissionInfo.setUserName(user.getName());
                permissionInfo.setPermissionType(permission.getPermissionType().name());
                permissionInfo.setCreatedAt(permission.getCreatedAt());
                permissionInfo.setUpdatedAt(permission.getUpdatedAt());
                result.add(permissionInfo);
            }
        }

        return result;
    }

    /**
     * Inner class để hold thông tin permission + user info
     */
    public static class ProjectPermissionWithUserInfo {
        private String permissionId;
        private String userId;
        private String userEmail;
        private String userName;
        private String permissionType;
        private java.time.LocalDateTime createdAt;
        private java.time.LocalDateTime updatedAt;

        // Getters and Setters
        public String getPermissionId() {
            return permissionId;
        }

        public void setPermissionId(String permissionId) {
            this.permissionId = permissionId;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getUserEmail() {
            return userEmail;
        }

        public void setUserEmail(String userEmail) {
            this.userEmail = userEmail;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getPermissionType() {
            return permissionType;
        }

        public void setPermissionType(String permissionType) {
            this.permissionType = permissionType;
        }

        public java.time.LocalDateTime getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(java.time.LocalDateTime createdAt) {
            this.createdAt = createdAt;
        }

        public java.time.LocalDateTime getUpdatedAt() {
            return updatedAt;
        }

        public void setUpdatedAt(java.time.LocalDateTime updatedAt) {
            this.updatedAt = updatedAt;
        }
    }
} 