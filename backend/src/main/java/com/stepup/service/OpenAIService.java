package com.stepup.service;

import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import com.theokanning.openai.service.OpenAiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.Base64;
import java.util.List;

@Slf4j
@Service
public class OpenAIService {
    private static final String DEFAULT_MODEL = "gpt-4";
    private static final Integer DEFAULT_MAX_TOKENS = 2000;
    private static final Double DEFAULT_TEMPERATURE = 0.7;
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(360);

    public OpenAIService() {
        // Empty constructor since we'll create OpenAiService instance per method
    }

    private OpenAiService createOpenAiService(String apiKey) {
        return new OpenAiService(apiKey, DEFAULT_TIMEOUT);
    }

    /**
     * Send a message to ChatGPT API with default settings
     */
    public String generateResponse(String prompt, List<ChatMessage> messages, String apiKey) {
        return generateResponse(prompt, messages, apiKey, DEFAULT_MODEL, DEFAULT_MAX_TOKENS, DEFAULT_TEMPERATURE);
    }

    /**
     * Send a message to ChatGPT API with custom settings
     */
    public String generateResponse(String prompt, List<ChatMessage> messages, String apiKey, String model, Integer maxTokens,
                                   Double temperature) {
        log.info("Sending request to ChatGPT API with prompt: {}, model: {}, maxTokens: {}, temperature: {}",
                prompt, model, maxTokens, temperature);

        try {
            OpenAiService openAiService = createOpenAiService(apiKey);

            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(model)
                    .messages(messages)
                    .temperature(temperature)
                    .maxTokens(maxTokens)
                    .build();

            ChatCompletionResult response = openAiService.createChatCompletion(request);

            if (response != null && !response.getChoices().isEmpty()) {
                String responseText = response.getChoices().get(0).getMessage().getContent();
                log.info("Received response from ChatGPT API: {}", responseText);
                return responseText;
            } else {
                log.error("Empty response from ChatGPT API");
                return "Sorry, I couldn't get a response from ChatGPT at this time.";
            }
        } catch (Exception e) {
            log.error("Error calling ChatGPT API: ", e);
            return "Error: " + e.getMessage();
        }
    }

    /**
     * Send a conversation history to ChatGPT API with default settings
     */
    public String generateResponseWithHistory(List<ChatMessage> messages, String apiKey) {
        return generateResponseWithHistory(messages, apiKey, DEFAULT_MODEL, DEFAULT_MAX_TOKENS,
                DEFAULT_TEMPERATURE);
    }

    /**
     * Send a conversation history to ChatGPT API with custom settings
     */
    public String generateResponseWithHistory(List<ChatMessage> messages,
                                              String apiKey, String model, Integer maxTokens, Double temperature) {
        log.info("Sending request to ChatGPT API with {} messages in conversation history, model: {}, maxTokens: {}, temperature: {}",
                messages.size(), model, maxTokens, temperature);

        try {
            OpenAiService openAiService = createOpenAiService(apiKey);
            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(model)
                    .messages(messages)
                    .temperature(temperature)
                    .maxTokens(maxTokens)
                    .build();

            ChatCompletionResult response = openAiService.createChatCompletion(request);

            if (response != null && !response.getChoices().isEmpty()) {
                String responseText = response.getChoices().get(0).getMessage().getContent();
                log.info("Received response from ChatGPT API: {}", responseText);
                return responseText;
            } else {
                log.error("Empty response from ChatGPT API");
                return "Sorry, I couldn't get a response from ChatGPT at this time.";
            }
        } catch (Exception e) {
            log.error("Error calling ChatGPT API: ", e);
            return "Error: " + e.getMessage();
        }
    }

    /**
     * Extract text content from different file types
     */
    private String extractFileContent(Path filePath) throws IOException {
        String fileName = filePath.getFileName().toString().toLowerCase();

        if (fileName.endsWith(".pdf")) {
            return extractPdfContent(filePath);
        } else if (fileName.endsWith(".docx")) {
            return extractDocxContent(filePath);
        } else if (fileName.endsWith(".txt") || fileName.endsWith(".java") ||
                fileName.endsWith(".html") || fileName.endsWith(".xml") ||
                fileName.endsWith(".json") || fileName.endsWith(".md")) {
            // Text files
            return Files.readString(filePath);
        } else {
            // For other binary files, convert to base64
            byte[] fileContent = Files.readAllBytes(filePath);
            return "File content (base64): " + Base64.getEncoder().encodeToString(fileContent);
        }
    }

    private String extractPdfContent(Path filePath) throws IOException {
        try (PDDocument document = PDDocument.load(filePath.toFile())) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }

    private String extractDocxContent(Path filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath.toFile());
             XWPFDocument document = new XWPFDocument(fis)) {
            XWPFWordExtractor extractor = new XWPFWordExtractor(document);
            return extractor.getText();
        }
    }

    /**
     * Send a file message to ChatGPT API with custom settings
     * If filePath is null, only the text parameter will be used for user message
     */
    public String generateResponseWithFile(String prompt, String text, String filePath, String apiKey,
                                           String model, Integer maxTokens, Double temperature) throws IOException {
        log.info("Sending request to ChatGPT API with prompt: {}, model: {}", prompt, model);
        if (filePath != null) {
            log.info("Including file: {}", filePath);
        }

        try {
            OpenAiService openAiService = createOpenAiService(apiKey);
            String userMessageContent = text;

            if (filePath != null) {
                Path path = Path.of(filePath);
                String fileName = path.getFileName().toString();
                userMessageContent = text + ". " + extractFileContent(path);
            }

            List<ChatMessage> messages = List.of(
                    new ChatMessage(ChatMessageRole.SYSTEM.value(), prompt),
                    new ChatMessage(ChatMessageRole.USER.value(), userMessageContent)
            );

            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(model)
                    .messages(messages)
                    .temperature(temperature)
                    .maxTokens(maxTokens)
                    .build();

            ChatCompletionResult response = openAiService.createChatCompletion(request);

            if (response != null && !response.getChoices().isEmpty()) {
                String responseText = response.getChoices().get(0).getMessage().getContent();
                log.info("Received response from ChatGPT API");
                return responseText;
            } else {
                log.error("Empty response from ChatGPT API");
                return "Sorry, I couldn't get a response from ChatGPT at this time.";
            }
        } catch (Exception e) {
            log.error("Error calling ChatGPT API: ", e);
            return "Error: " + e.getMessage();
        }
    }
} 