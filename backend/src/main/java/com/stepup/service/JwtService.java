package com.stepup.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Service
public class JwtService {

    private static final Logger log = LoggerFactory.getLogger(JwtService.class);

    @Value("${app.jwt.secret}")
    private String jwtSecret;

    @Value("${app.jwt.expiration-ms:86400000}") // 24 hours
    private int jwtExpirationMs;

    // Cache the key to avoid regenerating it every time
    private SecretKey signingKey;

    public String generateTokenForUser(String email, String userId, String role) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("email", email);
        claims.put("role", role);
        return createToken(claims, email);
    }

    public String createToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + jwtExpirationMs))
                .signWith(getSignKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    public Claims extractAllClaims(String token) {
        return Jwts.parser()
                .setSigningKey(getSignKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    public Boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    public Boolean validateToken(String token, String userEmail) {
        try {
            final String username = extractUsername(token);
            return (username.equals(userEmail) && !isTokenExpired(token));
        } catch (JwtException | IllegalArgumentException e) {
            log.error("JWT validation error: {}", e.getMessage());
            return false;
        }
    }

    private Key getSignKey() {
        if (signingKey == null) {
            // Check if the provided secret is secure enough (at least 32 bytes when base64 decoded)
            if (isSecretSecure(jwtSecret)) {
                byte[] keyBytes = Decoders.BASE64.decode(jwtSecret);
                signingKey = Keys.hmacShaKeyFor(keyBytes);
                log.info("Using provided JWT secret");
            } else {
                // Use the recommended approach to generate a secure key
                signingKey = Jwts.SIG.HS256.key().build();
                log.warn("Provided JWT secret is not secure enough (must be at least 256 bits). Generated a secure key instead. " +
                        "For production, please provide a secure base64-encoded secret of at least 32 bytes.");
            }
        }
        return signingKey;
    }

    private boolean isSecretSecure(String secret) {
        try {
            byte[] keyBytes = Decoders.BASE64.decode(secret);
            return keyBytes.length >= 32; // 256 bits = 32 bytes
        } catch (Exception e) {
            // If it's not valid base64, treat as plain text
            return secret.getBytes().length >= 32;
        }
    }
}