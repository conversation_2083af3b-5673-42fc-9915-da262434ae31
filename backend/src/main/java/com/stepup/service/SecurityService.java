package com.stepup.service;

import com.stepup.model.project.ProjectPermission;
import com.stepup.repository.FeatureRepository;
import org.springframework.stereotype.Service;

@Service("securityService")
public class SecurityService extends CommonService {
    private final ProjectPermissionService projectPermissionService;

    private final FeatureRepository featureRepository;

    public SecurityService(ProjectPermissionService projectPermissionService, FeatureRepository featureRepository) {
        this.projectPermissionService = projectPermissionService;
        this.featureRepository = featureRepository;
    }

    /**
     * <PERSON>ểm tra user có quyền VIEW trên project không
     */
    public boolean canViewProject(String projectId, String featureId) {
        String currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }

        if (projectId == null) {
            projectId = featureRepository.findProjectIdByFeatureId(featureId).orElseThrow(
                    () -> new RuntimeException("Project not found for feature id: " + featureId)
            );
        }

        return projectPermissionService.hasPermission(projectId, currentUserId, ProjectPermission.PermissionType.VIEW);
    }

    /**
     * Kiểm tra user có quyền EDIT trên project không
     */
    public boolean canEditProject(String projectId, String featureId) {
        String currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }

        if (projectId == null) {
            projectId = featureRepository.findProjectIdByFeatureId(featureId).orElse(null);
        }

        return projectPermissionService.hasPermission(projectId, currentUserId, ProjectPermission.PermissionType.EDIT);
    }

    /**
     * Kiểm tra user có thể quản lý permissions của project không
     */
    public boolean canManageProjectPermissions(String projectId, String featureId) {
        String currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }

        if (projectId == null) {
            projectId = featureRepository.findProjectIdByFeatureId(featureId).orElse(null);
        }

        return projectPermissionService.canManagePermissions(projectId, currentUserId);
    }
} 