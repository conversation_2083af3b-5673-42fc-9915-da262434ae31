package com.stepup.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.mapper.ChecklistParser;
import com.stepup.model.User;
import com.stepup.model.ai.AIPromptBucket;
import com.stepup.model.ai.ModelAIProvider;
import com.stepup.model.checklist.ChecklistTemplate;
import com.stepup.model.test.TestCase;
import com.stepup.model.testCase.CategoryResult;
import com.stepup.model.testCase.ChecklistItem;
import com.stepup.repository.*;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class ChecklistService extends CommonService {
    private static final Logger log = LoggerFactory.getLogger(ChecklistService.class);
    private final ObjectMapper objectMapper;
    private final OpenAIService openAIService;
    private final ChecklistParser checklistParser;
    private static final String UPLOAD_DIR = "temp_uploads";

    private final AIPromptBucketRepository aiPromptBucketRepository;
    private final AIProviderTokensRepository aiProviderTokensRepository;
    private final ChecklistTemplateRepository checklistTemplateRepository;
    private final ChecklistTemplateManagerService checklistTemplateManagerService;
    private final TestCaseRepository testCaseRepository;
    private final FeatureRepository featureRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    public ChecklistService(ObjectMapper objectMapper, OpenAIService openAIService,
                            ChecklistParser checklistParser,
                            AIPromptBucketRepository aiPromptBucketRepository,
                            AIProviderTokensRepository aiProviderTokensRepository,
                            ChecklistTemplateRepository checklistTemplateRepository,
                            ChecklistTemplateManagerService checklistTemplateManagerService,
                            TestCaseRepository testCaseRepository, FeatureRepository featureRepository) {
        this.objectMapper = objectMapper;
        this.openAIService = openAIService;
        this.checklistParser = checklistParser;
        this.aiPromptBucketRepository = aiPromptBucketRepository;
        this.aiProviderTokensRepository = aiProviderTokensRepository;
        this.checklistTemplateRepository = checklistTemplateRepository;
        this.checklistTemplateManagerService = checklistTemplateManagerService;
        this.testCaseRepository = testCaseRepository;
        this.featureRepository = featureRepository;
    }

    @PreAuthorize("@securityService.canViewProject(null, #featureId)")
    @Transactional
    public JsonNode getChecklist(MultipartFile file, String text, String featureId) throws IOException {
        User user = retrieveUserFromRequest();

        ChecklistTemplate template = checklistTemplateRepository.findByFeatureIdAndIsActiveTrue(featureId).orElse(null);

        if (template != null) {
            int attempts = 0;
            int maxAttempts = 15;

            while (template.getData() == null && attempts < maxAttempts) {
                System.out.println("Attempt " + (attempts + 1) + "/" + maxAttempts + " - template.getData() is null, refreshing...");

                try {
                    Thread.sleep(3000); // Sleep for 3 seconds
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Thread was interrupted while waiting for template data", e);
                }

                // Force refresh from database using EntityManager
                try {
                    entityManager.refresh(template);
                } catch (Exception e) {
                    // Fallback to repository query if refresh fails
                    template = checklistTemplateRepository.findByFeatureIdAndIsActiveTrue(featureId).orElse(null);
                }

                attempts++;

                if (template != null) {
                    System.out.println("After refresh attempt " + attempts + ": template.getData() = " +
                            (template.getData() != null ? "NOT NULL (length=" + template.getData().length() + ")" : "NULL"));
                } else {
                    System.out.println("After refresh attempt " + attempts + ": template is NULL");
                    break;
                }
            }

            // Check final state and throw error if data is still not available
            if (template == null || template.getData() == null) {
                throw new RuntimeException("Template data is not available after " + maxAttempts + " attempts (maximum wait time: " + (maxAttempts * 3) + " seconds)");
            }

            System.out.println("Template data is available!!!!");

            String jsonParsed = checklistParser.parseChecklistToJson(template.getData());
            return objectMapper.readTree(jsonParsed);
        } else {
            System.out.println("No existing template found for featureId: " + featureId + ", creating new one...");

            return generateChecklist(file, text, featureId);
        }
    }

    @Transactional
    protected JsonNode generateChecklist(MultipartFile file, String text, String featureId) throws IOException {
        // Create template record immediately in database with separate transaction
        ChecklistTemplate template = checklistTemplateManagerService.createTemplateRecordImmediately(featureId);

        AIPromptBucket prompt = aiPromptBucketRepository.findByName("gen_checklist_v1").get();
        String apiKey = aiProviderTokensRepository.findByProvider(ModelAIProvider.OPENAI).get().getToken();
        String response;

        if (file == null) {
            response = openAIService.generateResponseWithFile(
                    prompt.getPrompt().replace("{{res_language}}", prompt.getResponseLanguage()),
                    text,
                    null,  // filePath is null
                    apiKey,
                    prompt.getModel(),
                    Integer.parseInt(prompt.getMaxToken()),
                    Double.parseDouble(prompt.getTemperature())
            );
        } else {
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename != null ?
                    originalFilename.substring(originalFilename.lastIndexOf(".")) : "";
            String uniqueFilename = UUID.randomUUID() + extension;

            // Create upload directory if it doesn't exist
            Path uploadDir = Path.of(UPLOAD_DIR);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // Save file temporarily
            Path tempFilePath = uploadDir.resolve(uniqueFilename);
            try {
                Files.copy(file.getInputStream(), tempFilePath, StandardCopyOption.REPLACE_EXISTING);

                // Call OpenAI service to generate checklist
                response = openAIService.generateResponseWithFile(
                        prompt.getPrompt(),
                        text,
                        tempFilePath.toString(),
                        apiKey,
                        prompt.getModel(),
                        Integer.parseInt(prompt.getMaxToken()),
                        Double.parseDouble(prompt.getTemperature())
                );

            } finally {
                // Clean up temporary file
                Files.deleteIfExists(tempFilePath);
            }
        }

        String parsedJson = "";

        if (prompt.getResponseType().equals("json")) {
            parsedJson = response;
        } else if (prompt.getResponseType().equals("markdown")) {
            // Parse the response text to JSON format
            parsedJson = checklistParser.parseChecklistToJson(response);
        }

        // Update the template with generated data using separate transaction
        checklistTemplateManagerService.updateTemplateDataImmediately(template.getId(), response);

        return objectMapper.readTree(parsedJson);
    }

    @Transactional
    public JsonNode generateTestcase(String featureId) throws IOException {
        String testCaseMarkdown = featureRepository.findFeatureDataByFeatureId(featureId);

        AIPromptBucket prompt = aiPromptBucketRepository.findByName("gen_testcase_v1").get();

        if (testCaseMarkdown == null) {
            String apiKey = aiProviderTokensRepository.findByProvider(ModelAIProvider.OPENAI).get().getToken();
            // Process each checklist item individually and map results back
            testCaseMarkdown = processChecklistItemsInParallelWithMapping(featureId, prompt, apiKey);

            featureRepository.updateFeatureData(featureId, testCaseMarkdown);
        }

        String parsedJson;

        if (prompt.getResponseType().equals("json")) {
            parsedJson = testCaseMarkdown;
        } else if (prompt.getResponseType().equals("markdown")) {
            // Parse the response text to JSON format
            parsedJson = checklistParser.parseTestCasesToJson(testCaseMarkdown);
        } else {
            // Default: treat as text and parse to JSON
            parsedJson = checklistParser.parseTestCasesToJson(testCaseMarkdown);
        }

        return objectMapper.readTree(parsedJson);
    }

    /**
     * Process categories in parallel and map results back to individual checklist items
     *
     * @param prompt AI prompt configuration
     * @param apiKey OpenAI API key
     * @return Mapped result with original items and their corresponding test cases
     */
    private String processChecklistItemsInParallelWithMapping(String featureId, AIPromptBucket prompt, String apiKey) {
        String checkListMarkdown = checklistTemplateRepository.getChecklistDataByFeatureId(featureId);

        List<String> categories = parseMarkdownToSections(checkListMarkdown);


        if (categories.isEmpty()) {
            log.warn("No categories found in text");
            return "No categories found to process.";
        }

        // Create a fixed thread pool for parallel execution
        ExecutorService executor = Executors.newFixedThreadPool(Math.min(categories.size(), 10));

        try {
            // Create CompletableFuture for each category
            List<CompletableFuture<CategoryResult>> futures = categories.stream()
                    .map(categoryText -> CompletableFuture.supplyAsync(() -> {
                        try {
                            // Extract checklist items from this category
                            List<ChecklistItem> itemsInCategory = parseChecklistItems(categoryText);

                            // Send whole category to OpenAI
                            String categoryResponse = getOpenAIResponse(categoryText, prompt, apiKey);

                            return new CategoryResult(categoryText, itemsInCategory, categoryResponse);
                        } catch (Exception e) {
                            log.error("Error processing category: " + categoryText.substring(0, Math.min(100, categoryText.length())), e);
                            return new CategoryResult(categoryText, new ArrayList<>(), "Error processing category: " + e.getMessage());
                        }
                    }, executor))
                    .toList();

            // Wait for all futures to complete and collect results
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            // Get all results and map them back to individual items
            String finalResult = allFutures.thenApply(v -> {
                List<CategoryResult> results = futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList());

                // Save test cases to database
                saveTestCasesToDatabase(results, featureId);

                return results.stream()
                        .map(this::mapCategoryResponseToItems)
                        .collect(Collectors.joining("\n\n"));
            }).join();

            log.info("Successfully processed {} categories in parallel", categories.size());
            return finalResult;

        } finally {
            // Shutdown the executor
            executor.shutdown();
        }
    }

    /**
     * Parse checklist items with pattern [number] from text
     *
     * @param text Input text containing checklist items
     * @return List of ChecklistItem objects
     */
    private List<ChecklistItem> parseChecklistItems(String text) {
        List<ChecklistItem> items = new ArrayList<>();

        // Pattern to match [number] followed by text until next [number] or end
        Pattern pattern = Pattern.compile("\\[(\\d+)\\]\\s*([^\\[]*?)(?=\\s*\\[\\d+\\]|$)", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            String number = matcher.group(1);
            String itemText = matcher.group(2).trim();

            if (!itemText.isEmpty()) {
                items.add(new ChecklistItem(number, "[" + number + "] " + itemText));
            }
        }

        log.info("Parsed {} checklist items from text", items.size());
        return items;
    }

    /**
     * Map category response back to individual checklist items while preserving original structure
     *
     * @param categoryResult CategoryResult containing original category, items and OpenAI response
     * @return Formatted string with original structure + mapped results
     */
    private String mapCategoryResponseToItems(CategoryResult categoryResult) {
        List<ChecklistItem> items = categoryResult.getItemsInCategory();
        String response = categoryResult.getCategoryResponse();
        String originalCategoryText = categoryResult.getOriginalCategoryText();

        if (items.isEmpty()) {
            return originalCategoryText; // Return original text if no items found
        }

        // Try to split response into individual test cases
        List<String> testCases = parseTestCasesFromResponse(response);

        // Parse the original text and insert mapped results
        return insertMappedResultsIntoOriginalText(originalCategoryText, items, testCases);
    }

    /**
     * Insert mapped test case results into original category text at appropriate positions
     *
     * @param originalText Original category text with headers and checklist items
     * @param items List of parsed checklist items
     * @param testCases List of generated test cases
     * @return Original text with test cases inserted after each checklist item
     */
    private String insertMappedResultsIntoOriginalText(String originalText, List<ChecklistItem> items, List<String> testCases) {
        StringBuilder result = new StringBuilder();
        String[] lines = originalText.split("\n");

        int currentItemIndex = 0;

        for (String line : lines) {
            // Add the original line
            result.append(line).append("\n");

            // Check if this line contains a checklist item
            if (currentItemIndex < items.size()) {
                ChecklistItem currentItem = items.get(currentItemIndex);

                // Check if current line contains the current checklist item
                if (line.trim().startsWith("[" + currentItem.getNumber() + "]")) {
                    // Insert corresponding test case after this line
                    String testCase = currentItemIndex < testCases.size() ?
                            testCases.get(currentItemIndex) :
                            "Test case not found for this item.";

                    result.append(testCase).append("\n");
                    currentItemIndex++;
                }
            }
        }

        // Handle any remaining test cases that weren't inserted
        while (currentItemIndex < testCases.size()) {
            result.append("\n").append(testCases.get(currentItemIndex)).append("\n");
            currentItemIndex++;
        }

        return result.toString().trim();
    }

    /**
     * Parse individual test cases from OpenAI response
     * Assumes test cases are separated by "### **Test Case:" pattern
     *
     * @param response OpenAI response containing multiple test cases
     * @return List of individual test cases
     */
    private List<String> parseTestCasesFromResponse(String response) {
        List<String> testCases = new ArrayList<>();

        // Split by test case headers
        String[] parts = response.split("### \\*\\*Test Case:");

        for (int i = 1; i < parts.length; i++) { // Skip first empty part
            String testCase = "### **Test Case:" + parts[i].trim();
            testCases.add(testCase);
        }

        // If no test case pattern found, return the whole response as single item
        if (testCases.isEmpty() && !response.trim().isEmpty()) {
            testCases.add(response.trim());
        }

        return testCases;
    }

    /**
     * Save test cases to database from category results
     *
     * @param categoryResults List of CategoryResult containing processed data
     */
    @Transactional
    protected void saveTestCasesToDatabase(List<CategoryResult> categoryResults, String featureId) {
        try {
            for (CategoryResult categoryResult : categoryResults) {
                List<ChecklistItem> items = categoryResult.getItemsInCategory();
                String response = categoryResult.getCategoryResponse();

                if (items.isEmpty()) {
                    continue;
                }

                // Parse test cases from response
                List<String> txtTestCases = parseTestCasesFromResponse(response);

                List<TestCase> testCases = new ArrayList<>();

                // Create and save TestCase for each item
                for (int i = 0; i < items.size(); i++) {
                    ChecklistItem item = items.get(i);
                    String testCaseData = i < txtTestCases.size() ? txtTestCases.get(i) : "Test case not found for this item.";

                    // Create formatted data (original item + test case)
                    String formattedData = item.getText() + "\n" + testCaseData;

                    // Extract test case ID from generated content (if available)
                    String testCaseId = extractTestCaseId(testCaseData);

                    TestCase testCase = TestCase.builder()
                            .featureId(featureId)
                            .testCaseOrder(i)
                            .data(formattedData)
                            .priority(Integer.parseInt(item.getNumber()))
                            .status(TestCase.TestCaseStatus.PENDING)
                            .build();

                    testCases.add(testCase);
                    log.debug("Saved test case for item [{}]: {}", item.getNumber(), testCaseId);
                }

                testCaseRepository.saveAll(testCases);
            }

            log.info("Successfully saved test cases to database");

        } catch (Exception e) {
            log.error("Error saving test cases to database", e);
            throw new RuntimeException("Failed to save test cases", e);
        }
    }

    /**
     * Extract test case ID from generated test case content
     *
     * @param testCaseData Generated test case content
     * @return Extracted test case ID or null
     */
    private String extractTestCaseId(String testCaseData) {
        try {
            // Look for pattern like "TC_SECURITY_001"
            Pattern pattern = Pattern.compile("TC_[A-Z_]+_\\d+");
            Matcher matcher = pattern.matcher(testCaseData);

            if (matcher.find()) {
                return matcher.group();
            }
        } catch (Exception e) {
            log.debug("Could not extract test case ID from content", e);
        }

        return null;
    }

    private String getOpenAIResponse(String text, AIPromptBucket prompt, String apiKey) {
        List<ChatMessage> messages = List.of(
                new ChatMessage(ChatMessageRole.SYSTEM.value(), prompt.getPrompt().replace("{{res_language}}", prompt.getResponseLanguage())),
                new ChatMessage(ChatMessageRole.USER.value(), text)
        );

        String response;

        response = openAIService.generateResponse(
                prompt.getPrompt(),
                messages,
                apiKey,
                prompt.getModel(),
                Integer.parseInt(prompt.getMaxToken()),
                Double.parseDouble(prompt.getTemperature())
        );
        return response;
    }

    /**
     * Tách markdown text thành các cụm từ dựa trên các header cấp 1 (chỉ bắt đầu bằng một ký tự #)
     * Mỗi cụm từ bao gồm nội dung từ một header # tới header # tiếp theo (không bao gồm ##, ###, v.v.)
     *
     * @param markdownText văn bản markdown cần tách
     * @return danh sách các section/chunk của markdown
     */
    public List<String> parseMarkdownToSections(String markdownText) {
        List<String> sections = new ArrayList<>();

        if (markdownText == null || markdownText.trim().isEmpty()) {
            return sections;
        }

        String[] lines = markdownText.split("\n");
        StringBuilder currentSection = new StringBuilder();
        boolean hasStartedSection = false;

        for (String line : lines) {
            String trimmedLine = line.trim();

            // Kiểm tra nếu dòng bắt đầu bằng chính xác một ký tự # (header cấp 1)
            if (trimmedLine.startsWith("#") && !trimmedLine.startsWith("##")) {
                // Nếu đã có section trước đó, lưu nó vào danh sách
                if (hasStartedSection && !currentSection.isEmpty()) {
                    sections.add(currentSection.toString().trim());
                    currentSection = new StringBuilder();
                }

                // Bắt đầu section mới
                hasStartedSection = true;
                currentSection.append(line).append("\n");
            } else {
                // Nếu đã bắt đầu một section, thêm dòng này vào section hiện tại
                // (bao gồm cả ##, ###, nội dung thường, v.v.)
                if (hasStartedSection) {
                    currentSection.append(line).append("\n");
                }
            }
        }

        // Thêm section cuối cùng nếu có
        if (hasStartedSection && !currentSection.isEmpty()) {
            sections.add(currentSection.toString().trim());
        }

        return sections;
    }
} 