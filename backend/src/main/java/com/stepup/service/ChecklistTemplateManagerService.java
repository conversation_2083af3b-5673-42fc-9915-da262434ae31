package com.stepup.service;

import com.stepup.model.checklist.ChecklistTemplate;
import com.stepup.repository.ChecklistTemplateRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ChecklistTemplateManagerService {

    @Autowired
    private ChecklistTemplateRepository checklistTemplateRepository;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Create template record immediately with separate transaction
     * This ensures the record is committed to database right away
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ChecklistTemplate createTemplateRecordImmediately(String featureId) {
        ChecklistTemplate template = new ChecklistTemplate();
        template.setFeatureId(featureId);
        template.setIsActive(true);
        template.setVersion("1.0");
        template = checklistTemplateRepository.save(template);

        // Flush to ensure immediate persistence (optional with REQUIRES_NEW but safe)
        entityManager.flush();
        System.out.println("✅ NEW TRANSACTION COMMITTED: Created template record with ID: " + template.getId() + " for featureId: " + featureId);

        return template;
    }

    /**
     * Update template data immediately with separate transaction
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTemplateDataImmediately(String templateId, String data) {
        ChecklistTemplate template = checklistTemplateRepository.findById(templateId)
                .orElseThrow(() -> new RuntimeException("Template not found with id: " + templateId));

        template.setData(data);
        checklistTemplateRepository.save(template);

        // Flush to ensure immediate persistence
        entityManager.flush();
        System.out.println("✅ NEW TRANSACTION COMMITTED: Updated template data for ID: " + templateId);
    }
} 