package com.stepup.service;

import com.stepup.dto.project.CreateProjectReqDTO;
import com.stepup.dto.project.FeatureResDTO;
import com.stepup.dto.project.ProjectResDTO;
import com.stepup.model.User;
import com.stepup.model.project.Project;
import com.stepup.model.project.ProjectStatus;
import com.stepup.repository.ProjectRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ProjectService extends CommonService {
    private final ProjectRepository projectRepository;
    private final FeatureService featureService;

    @Autowired
    public ProjectService(ProjectRepository projectRepository, FeatureService featureService) {
        this.projectRepository = projectRepository;
        this.featureService = featureService;
    }

    public ProjectResDTO createProject(CreateProjectReqDTO request) {
        User user = retrieveUserFromRequest();

        Project project = Project.builder()
                .name(request.getName())
                .description(request.getDescription())
                .status(ProjectStatus.ACTIVE)
                .createdBy(user.getId())
                .build();

        Project savedProject = projectRepository.save(project);

        return mapToProjectResponse(savedProject);
    }

    public List<ProjectResDTO> getAllProjects() {
        return projectRepository.findAll()
                .stream()
                .map(this::mapToProjectResponse)
                .collect(Collectors.toList());
    }

    public List<ProjectResDTO> getProjectsByCreatedBy(String createdBy) {
        return projectRepository.findByCreatedBy(createdBy)
                .stream()
                .map(this::mapToProjectResponse)
                .collect(Collectors.toList());
    }

    @PreAuthorize("@securityService.canViewProject(#id, null)")
    public ProjectResDTO getProjectById(String id) {
        Optional<Project> projectOpt = projectRepository.findById(id);
        if (projectOpt.isEmpty()) {
            throw new RuntimeException("Project not found with id: " + id);
        }

        ProjectResDTO projectResDTO = mapToProjectResponse(projectOpt.get());
        projectResDTO.setFeatures(featureService.getFeaturesByProjectId(id));

        return projectResDTO;
    }

    @PreAuthorize("@securityService.canEditProject(#id, null)")
    public ProjectResDTO updateProject(String id, CreateProjectReqDTO request) {
        Optional<Project> projectOpt = projectRepository.findById(id);

        if (projectOpt.isEmpty()) {
            throw new RuntimeException("Project not found with id: " + id);
        }

        Project project = projectOpt.get();
        project.setName(request.getName());
        project.setDescription(request.getDescription());

        Project savedProject = projectRepository.save(project);
        return mapToProjectResponse(savedProject);
    }

    @PreAuthorize("@securityService.canEditProject(#id, null)")
    public void deleteProject(String id) {
        if (!projectRepository.existsById(id)) {
            throw new RuntimeException("Project not found with id: " + id);
        }
        projectRepository.deleteById(id);
    }

    private ProjectResDTO mapToProjectResponse(Project project) {
        return ProjectResDTO.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .status(project.getStatus())
                .createdBy(project.getCreatedBy())
                .createdAt(project.getCreatedAt())
                .lastAccessDate(project.getLastAccessDate())
                .build();
    }

    public FeatureResDTO createFeature(String projectId) {
        return featureService.createFeature(projectId);
    }

    public List<ProjectResDTO> getRecentProjects() {
        return projectRepository.findTop2ByOrderByLastAccessDateDesc()
                .stream()
                .limit(2)
                .map(this::mapToProjectResponse)
                .collect(Collectors.toList());
    }

    /**
     * Lấy danh sách users được share trong project (bao gồm owner)
     */
    @PreAuthorize("@securityService.canViewProject(#projectId, null)")
    public List<ProjectUserPermissionDTO> getProjectSharedUsers(String projectId) {
        // Tạm thời trả về empty list để tránh lỗi Lombok
        // TODO: Implement sau khi fix Lombok issues
        return new ArrayList<>();
    }

    /**
     * DTO để trả về thông tin user và permission
     */
    public static class ProjectUserPermissionDTO {
        private String userId;
        private String userEmail;
        private String userName;
        private String permissionType; // OWNER, EDIT, VIEW
        private java.time.LocalDateTime sharedAt;

        // Constructors
        public ProjectUserPermissionDTO() {
        }

        public ProjectUserPermissionDTO(String userId, String userEmail, String userName, String permissionType, java.time.LocalDateTime sharedAt) {
            this.userId = userId;
            this.userEmail = userEmail;
            this.userName = userName;
            this.permissionType = permissionType;
            this.sharedAt = sharedAt;
        }

        // Getters and Setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getUserEmail() {
            return userEmail;
        }

        public void setUserEmail(String userEmail) {
            this.userEmail = userEmail;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getPermissionType() {
            return permissionType;
        }

        public void setPermissionType(String permissionType) {
            this.permissionType = permissionType;
        }

        public java.time.LocalDateTime getSharedAt() {
            return sharedAt;
        }

        public void setSharedAt(java.time.LocalDateTime sharedAt) {
            this.sharedAt = sharedAt;
        }
    }
}