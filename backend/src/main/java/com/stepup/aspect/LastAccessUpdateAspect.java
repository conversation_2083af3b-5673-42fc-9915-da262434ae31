package com.stepup.aspect;

import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.time.LocalDateTime;

@Aspect
@Component
public class LastAccessUpdateAspect {
    private static final Logger logger = LoggerFactory.getLogger(LastAccessUpdateAspect.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // Service layer pointcuts - these work reliably
    @AfterReturning(pointcut = "execution(* com.stepup.service.ProjectService.getProjectById(..))", returning = "result")
    public void updateProjectFromService(com.stepup.dto.project.ProjectResDTO result) {
        if (result != null) {
            String projectId = getProjectId(result);
            if (projectId != null) {
                logger.debug("AOP: Updating last access date for project ID: {}", projectId);
                updateProjectLastAccessSync(projectId);
            }
        }
    }

    // Track feature access when getChecklist is called
    @AfterReturning(pointcut = "execution(* com.stepup.service.ChecklistService.getChecklist(..)) && args(file, text, featureId)", returning = "result", argNames = "result,file,text,featureId")
    public void updateFeatureFromGetChecklist(Object result, org.springframework.web.multipart.MultipartFile file, String text, String featureId) {
        if (featureId != null && !featureId.trim().isEmpty()) {
            logger.debug("AOP: Updating last access date for feature from getChecklist, ID: {}", featureId);
            updateFeatureLastAccessSync(featureId);
        }
    }

    private String getProjectId(com.stepup.dto.project.ProjectResDTO project) {
        try {
            Field idField = project.getClass().getDeclaredField("id");
            idField.setAccessible(true);
            return (String) idField.get(project);
        } catch (Exception e) {
            logger.debug("Failed to get project ID using reflection", e);
            return null;
        }
    }

    private void updateProjectLastAccessSync(String projectId) {
        try {
            String sql = "UPDATE projects SET last_access_date = ? WHERE id = ?";
            int rowsAffected = jdbcTemplate.update(sql, LocalDateTime.now(), projectId);
            logger.debug("Updated last_access_date for project {}, rows affected: {}", projectId, rowsAffected);
        } catch (Exception e) {
            logger.error("Failed to update project last access date for ID: {}", projectId, e);
        }
    }

    private void updateFeatureLastAccessSync(String featureId) {
        try {
            String sql = "UPDATE features SET last_access_date = ? WHERE id = ?";
            int rowsAffected = jdbcTemplate.update(sql, LocalDateTime.now(), featureId);
            logger.debug("Updated last_access_date for feature {}, rows affected: {}", featureId, rowsAffected);
        } catch (Exception e) {
            logger.error("Failed to update feature last access date for ID: {}", featureId, e);
        }
    }

    private void updateProjectLastAccessAsync(String projectId, LocalDateTime accessTime) {
        try {
            String sql = "UPDATE projects SET last_access_date = ? WHERE id = ?";
            jdbcTemplate.update(sql, accessTime, projectId);
        } catch (Exception e) {
            logger.error("Failed to update project last access date for ID: {}", projectId, e);
        }
    }

    private void updateFeatureLastAccessAsync(String featureId, LocalDateTime accessTime) {
        try {
            String sql = "UPDATE features SET last_access_date = ? WHERE id = ?";
            jdbcTemplate.update(sql, accessTime, featureId);
        } catch (Exception e) {
            logger.error("Failed to update feature last access date for ID: {}", featureId, e);
        }
    }
} 