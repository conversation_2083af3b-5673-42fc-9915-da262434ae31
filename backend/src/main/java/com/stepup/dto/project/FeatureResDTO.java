package com.stepup.dto.project;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.model.project.Feature;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FeatureResDTO {
    @JsonProperty("id")
    private String id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("project_id")
    private String projectId;

    @JsonProperty("status")
    private Feature.FeatureStatus status;

    @JsonProperty("is_active")
    private Boolean isActive;

    @JsonProperty("last_access_date")
    private LocalDateTime lastAccessDate;
}
