package com.stepup.mapper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.stereotype.Component;

@Component
public class ChecklistParser {

    private final ObjectMapper objectMapper;

    public ChecklistParser(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public String parseChecklistToJson(String content) {
        try {
            ObjectNode root = objectMapper.createObjectNode();
            ObjectNode testChecklist = objectMapper.createObjectNode();
            ArrayNode categories = objectMapper.createArrayNode();

            String[] lines = content.split("\n");

            String title = "";
            ObjectNode currentCategory = null;
            ObjectNode currentSubcategory = null;
            int categoryCounter = 1;
            int subcategoryCounter = 1;
            int testCounter = 1;
            int totalChecks = 0;

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;

                // Parse feature title
                if (line.startsWith("F:")) {
                    title = line.substring(2).trim();
                    continue;
                }

                // Parse main category (single #)
                if (line.startsWith("#") && !line.startsWith("##")) {
                    // Finish previous category and subcategory
                    finishCategory(currentCategory, currentSubcategory);
                    if (currentCategory != null) {
                        categories.add(currentCategory);
                    }

                    // Create new category
                    currentCategory = objectMapper.createObjectNode();
                    currentCategory.put("id", String.valueOf(categoryCounter));
                    String categoryName = line.substring(1).trim(); // Remove # and trim
                    currentCategory.put("name", categoryName);
                    currentCategory.put("total_checks", 0);
                    currentCategory.set("subcategories", objectMapper.createArrayNode());

                    categoryCounter++;
                    subcategoryCounter = 1;
                    currentSubcategory = null;
                    continue;
                }

                // Parse subcategory (double ##)
                if (line.startsWith("##")) {
                    // Finish previous subcategory
                    finishSubcategory(currentSubcategory);
                    if (currentSubcategory != null && currentCategory != null) {
                        ArrayNode subcategories = (ArrayNode) currentCategory.get("subcategories");
                        subcategories.add(currentSubcategory);
                    }

                    // Create new subcategory
                    if (currentCategory != null) {
                        currentSubcategory = objectMapper.createObjectNode();
                        String subcategoryId = currentCategory.get("id").asText() + "." + subcategoryCounter;
                        currentSubcategory.put("id", subcategoryId);
                        String subcategoryName = line.substring(2).trim(); // Remove ## and trim
                        currentSubcategory.put("name", subcategoryName);
                        currentSubcategory.put("total_checks", 0);
                        currentSubcategory.set("tests", objectMapper.createArrayNode());

                        subcategoryCounter++;
                    }
                    continue;
                }

                // Parse test items [priority] description
                if (line.matches("^\\[\\d+\\].*")) {
                    if (currentSubcategory != null) {
                        // Extract priority and description
                        int closeBracket = line.indexOf(']');
                        if (closeBracket > 0) {
                            String priorityBracket = line.substring(0, closeBracket + 1);
                            String description = line.substring(closeBracket + 1).trim();
                            String priorityText = mapPriorityToText(priorityBracket);

                            // Create test object
                            ObjectNode test = objectMapper.createObjectNode();
                            test.put("id", testCounter);
                            test.put("name", description);
                            test.put("priority", priorityText);
                            test.putNull("testDataSuggestion");
                            test.put("status", "pending");

                            // Add to current subcategory
                            ArrayNode tests = (ArrayNode) currentSubcategory.get("tests");
                            tests.add(test);

                            testCounter++;
                            totalChecks++;
                        }
                    }
                    continue;
                }
            }

            // Finish the last category and subcategory
            finishCategory(currentCategory, currentSubcategory);
            if (currentCategory != null) {
                categories.add(currentCategory);
            }

            // Calculate totals for all categories
            calculateTotals(categories);

            // Build final JSON structure
            testChecklist.put("title", title);
            testChecklist.put("total_checks", totalChecks);
            testChecklist.set("categories", categories);

            root.set("test_checklist", testChecklist);

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(root);

        } catch (Exception e) {
            e.printStackTrace();
            return "{}";
        }
    }

    private void finishCategory(ObjectNode category, ObjectNode subcategory) {
        if (category != null) {
            finishSubcategory(subcategory);
            // Add the current subcategory to the category if it exists
            if (subcategory != null) {
                ArrayNode subcategories = (ArrayNode) category.get("subcategories");
                subcategories.add(subcategory);
            }
        }
    }

    private void finishSubcategory(ObjectNode subcategory) {
        if (subcategory != null) {
            ArrayNode tests = (ArrayNode) subcategory.get("tests");
            if (tests != null) {
                subcategory.put("total_checks", tests.size());
            }
        }
    }

    private void calculateTotals(ArrayNode categories) {
        int totalChecks = 0;

        for (int i = 0; i < categories.size(); i++) {
            ObjectNode category = (ObjectNode) categories.get(i);
            ArrayNode subcategories = (ArrayNode) category.get("subcategories");
            int categoryTotal = 0;

            for (int j = 0; j < subcategories.size(); j++) {
                ObjectNode subcategory = (ObjectNode) subcategories.get(j);
                int subcategoryTotal = subcategory.get("total_checks").asInt();
                categoryTotal += subcategoryTotal;
            }

            category.put("total_checks", categoryTotal);
            totalChecks += categoryTotal;
        }

        // Update total in root if needed
        if (categories.size() > 0) {
            ObjectNode firstCategory = (ObjectNode) categories.get(0);
            // Note: total_checks will be set in the main method
        }
    }

    public String parseTestCasesToJson(String content) {
        try {
            ObjectNode root = objectMapper.createObjectNode();
            ObjectNode testChecklist = objectMapper.createObjectNode();
            ArrayNode categories = objectMapper.createArrayNode();

            String[] lines = content.split("\n");

            String title = "";
            ObjectNode currentCategory = null;
            ObjectNode currentSubcategory = null;
            ObjectNode currentTest = null;
            int categoryCounter = 1;
            int subcategoryCounter = 1;
            int testCounter = 1;
            int totalChecks = 0;

            StringBuilder currentTestCase = new StringBuilder();
            boolean inTestCase = false;

            for (String line : lines) {
                String originalLine = line;
                line = line.trim();
                if (line.isEmpty()) {
                    if (inTestCase) {
                        currentTestCase.append("\n");
                    }
                    continue;
                }

                // Parse feature title (first line starting with F:)
                if (line.startsWith("F:")) {
                    title = line.substring(2).trim();
                    System.out.println("Found title: " + title);
                    continue;
                }

                // Parse main category (single #)
                if (line.startsWith("#") && !line.startsWith("##") && !line.startsWith("###")) {
                    // Finish previous test case if any
                    finishCurrentTestCase(currentTest, currentTestCase);

                    // Finish previous category and subcategory
                    finishCategory(currentCategory, currentSubcategory);
                    if (currentCategory != null) {
                        categories.add(currentCategory);
                    }

                    // Create new category
                    currentCategory = objectMapper.createObjectNode();
                    currentCategory.put("id", String.valueOf(categoryCounter));
                    String categoryName = line.substring(1).trim();
                    currentCategory.put("name", categoryName);
                    currentCategory.put("total_checks", 0);
                    currentCategory.set("subcategories", objectMapper.createArrayNode());

                    categoryCounter++;
                    subcategoryCounter = 1;
                    currentSubcategory = null;
                    currentTest = null;
                    inTestCase = false;
                    continue;
                }

                // Parse subcategory (double ##)
                if (line.startsWith("##") && !line.startsWith("###")) {
                    // Finish previous test case if any
                    finishCurrentTestCase(currentTest, currentTestCase);

                    // Finish previous subcategory
                    finishSubcategory(currentSubcategory);
                    if (currentSubcategory != null && currentCategory != null) {
                        ArrayNode subcategories = (ArrayNode) currentCategory.get("subcategories");
                        subcategories.add(currentSubcategory);
                    }

                    // Create new subcategory
                    if (currentCategory != null) {
                        currentSubcategory = objectMapper.createObjectNode();
                        String subcategoryId = currentCategory.get("id").asText() + "." + subcategoryCounter;
                        currentSubcategory.put("id", subcategoryId);
                        String subcategoryName = line.substring(2).trim();
                        currentSubcategory.put("name", subcategoryName);
                        currentSubcategory.put("total_checks", 0);
                        currentSubcategory.set("tests", objectMapper.createArrayNode());

                        subcategoryCounter++;
                    }

                    currentTest = null;
                    inTestCase = false;
                    continue;
                }

                // Parse test items [priority] description
                if (line.matches("^\\[\\d+\\].*")) {
                    // Finish previous test case if any
                    if (currentTest != null) {
                        finishCurrentTestCase(currentTest, currentTestCase);
                    }

                    if (currentSubcategory != null) {
                        // Extract priority and description
                        int closeBracket = line.indexOf(']');
                        if (closeBracket > 0) {
                            String priority = line.substring(0, closeBracket + 1);
                            String description = line.substring(closeBracket + 1).trim();

                            // Create test object
                            currentTest = objectMapper.createObjectNode();
                            currentTest.put("id", testCounter);
                            currentTest.put("name", description);
                            currentTest.put("priority", priority);
                            currentTest.putNull("testDataSuggestion");
                            currentTest.put("status", "pending");
                            currentTest.putNull("test_case"); // Will be filled later

                            // Add to current subcategory
                            ArrayNode tests = (ArrayNode) currentSubcategory.get("tests");
                            tests.add(currentTest);

                            testCounter++;
                            totalChecks++;
                            inTestCase = false;
                            currentTestCase.setLength(0);
                        }
                    }
                    continue;
                }

                // Parse test case start - more flexible pattern matching
                if (line.startsWith("### **Test Case:") || line.contains("**Test Case:") ||
                        line.startsWith("**Test Case:") || line.matches(".*Test Case:.*TC_.*")) {

                    System.out.println("Found test case start: " + line);
                    inTestCase = true;
                    currentTestCase.setLength(0);
                    currentTestCase.append(originalLine).append("\n");
                    continue;
                }

                // Collect test case content
                if (inTestCase) {
                    currentTestCase.append(originalLine).append("\n");

                    // Check if we reached end of test case (hitting next checklist item or section)
                    if (line.matches("^\\[\\d+\\].*") || line.startsWith("#")) {
                        // This line belongs to next item, finish current test case first
                        inTestCase = false;
                        finishCurrentTestCase(currentTest, currentTestCase);

                        // Process this line again (don't continue)
                        line = originalLine.trim();
                        originalLine = line;
                    } else {
                        continue;
                    }
                }
            }

            // Finish the last test case, category and subcategory
            finishCurrentTestCase(currentTest, currentTestCase);
            finishCategory(currentCategory, currentSubcategory);
            if (currentCategory != null) {
                categories.add(currentCategory);
            }

            // Calculate totals for all categories
            calculateTotals(categories);

            // Build final JSON structure
            testChecklist.put("title", title.isEmpty() ? "Generated Test Cases" : title);
            testChecklist.put("total_checks", totalChecks);
            testChecklist.set("categories", categories);

            System.out.println("Final title set: " + (title.isEmpty() ? "Generated Test Cases" : title));
            System.out.println("Total checks: " + totalChecks);

            root.set("test_checklist", testChecklist);

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(root);

        } catch (Exception e) {
            e.printStackTrace();
            return "{}";
        }
    }

    /**
     * Finish current test case by parsing and adding test case content
     */
    private void finishCurrentTestCase(ObjectNode currentTest, StringBuilder currentTestCase) {
        if (currentTest != null && currentTestCase.length() > 0) {
            String testCaseContent = currentTestCase.toString().trim();

            // Debug log
            System.out.println("Processing test case content (length: " + testCaseContent.length() + ")");
            System.out.println("First 100 chars: " + testCaseContent.substring(0, Math.min(100, testCaseContent.length())));

            // Check if content actually contains test case
            if (testCaseContent.contains("**Test Case:") || testCaseContent.contains("Test Case:")) {
                ObjectNode parsedTestCase = parseTestCaseSection(testCaseContent);

                if (parsedTestCase != null) {
                    currentTest.set("test_case", parsedTestCase);
                    System.out.println("Successfully parsed test case: " + parsedTestCase.get("id"));
                } else {
                    // If parsing fails, store raw content as string
                    currentTest.put("test_case", testCaseContent);
                    System.out.println("Failed to parse test case, storing raw content");
                }
            } else {
                System.out.println("No test case content found, content: " + testCaseContent);
                currentTest.putNull("test_case");
            }

            currentTestCase.setLength(0);
        } else {
            if (currentTest != null) {
                System.out.println("No test case content to process for test: " + currentTest.get("name"));
            }
        }
    }

    private ObjectNode parseTestCaseSection(String section) {
        try {
            ObjectNode testCase = objectMapper.createObjectNode();
            String[] lines = section.split("\n");

            String id = "";
            String title = "";
            StringBuilder description = new StringBuilder();
            StringBuilder source = new StringBuilder();
            ArrayNode preconditions = objectMapper.createArrayNode();
            ArrayNode steps = objectMapper.createArrayNode();
            StringBuilder additionalNotes = new StringBuilder();

            boolean inDescription = false;
            boolean inSource = false;
            boolean inPreconditions = false;
            boolean inSteps = false;
            boolean inNotes = false;
            int stepCounter = 1;

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty() || line.equals("---")) continue;

                // Extract test case ID and title
                if (line.startsWith("### **Test Case:") && line.contains("-")) {
                    String titleLine = line.substring(16).trim(); // Remove "### **Test Case:"
                    if (titleLine.endsWith("**")) {
                        titleLine = titleLine.substring(0, titleLine.length() - 2);
                    }

                    String[] parts = titleLine.split(" - ", 2);
                    if (parts.length >= 2) {
                        id = parts[0].trim();
                        title = parts[1].trim();
                    }
                    resetFlags();
                    continue;
                }

                // Extract description
                if (line.startsWith("**Description:**")) {
                    String desc = line.substring(16).trim();
                    description.setLength(0);
                    description.append(desc);
                    inDescription = true;
                    inSource = false;
                    inPreconditions = false;
                    inSteps = false;
                    inNotes = false;
                    continue;
                } else if (inDescription && !line.startsWith("**")) {
                    description.append(" ").append(line);
                    continue;
                }

                // Extract source
                if (line.startsWith("**Source (from Checklist):**")) {
                    String src = "";
                    if (line.length() > 29) {
                        src = line.substring(29).trim();
                    }
                    source.setLength(0);
                    source.append(src);
                    inDescription = false;
                    inSource = true;
                    inPreconditions = false;
                    inSteps = false;
                    inNotes = false;
                    continue;
                } else if (inSource && !line.startsWith("**") && !line.startsWith("---")) {
                    source.append(" ").append(line);
                    continue;
                }

                // Section markers
                if (line.equals("**Preconditions:**")) {
                    inDescription = false;
                    inSource = false;
                    inPreconditions = true;
                    inSteps = false;
                    inNotes = false;
                    continue;
                }

                if (line.equals("**Steps:**")) {
                    inDescription = false;
                    inSource = false;
                    inPreconditions = false;
                    inSteps = true;
                    inNotes = false;
                    stepCounter = 1;
                    continue;
                }

                if (line.startsWith("**Additional Notes")) {
                    inDescription = false;
                    inSource = false;
                    inPreconditions = false;
                    inSteps = false;
                    inNotes = true;
                    int colonIndex = line.indexOf(":");
                    if (colonIndex != -1 && colonIndex + 1 < line.length()) {
                        additionalNotes.setLength(0);
                        additionalNotes.append(line.substring(colonIndex + 1).trim());
                    }
                    continue;
                }

                // Parse preconditions
                if (inPreconditions && line.startsWith("*")) {
                    String precondition = line.substring(1).trim();
                    preconditions.add(precondition);
                    continue;
                }

                // Parse steps - handle the new format
                if (inSteps) {
                    if (line.matches("^\\d+\\..*")) {
                        // New step starting - extract action from the same line if present
                        ObjectNode step = objectMapper.createObjectNode();
                        step.put("stepNumber", stepCounter++);

                        // Check if action is on the same line after "**Action:**"
                        if (line.contains("**Action:**")) {
                            int actionIndex = line.indexOf("**Action:**");
                            if (actionIndex != -1) {
                                String action = line.substring(actionIndex + 11).trim();
                                step.put("action", action);
                            }
                        }

                        steps.add(step);
                        continue;
                    }

                    if (!steps.isEmpty()) {
                        ObjectNode currentStep = (ObjectNode) steps.get(steps.size() - 1);

                        if (line.startsWith("**Action:**")) {
                            String action = line.substring(11).trim();
                            currentStep.put("action", action);
                        } else if (line.startsWith("**Expected Result:**")) {
                            String expectedResult = line.substring(20).trim();
                            currentStep.put("expectedResult", expectedResult);
                        } else if (line.startsWith("**Test Data:**")) {
                            String testData = line.substring(14).trim();
                            currentStep.put("testData", testData);
                        } else if (line.startsWith("*") && !line.startsWith("**")) {
                            // Handle bullet point test data
                            String existingTestData = currentStep.has("testData") ? currentStep.get("testData").asText() : "";
                            String newTestData = line.substring(1).trim();
                            if (!existingTestData.isEmpty() && !existingTestData.equals("N/A")) {
                                currentStep.put("testData", existingTestData + "; " + newTestData);
                            } else {
                                currentStep.put("testData", newTestData);
                            }
                        }
                    }
                    continue;
                }

                // Additional notes continuation
                if (inNotes && !line.startsWith("---")) {
                    if (line.startsWith("*") && !line.startsWith("**")) {
                        // Handle bullet points in additional notes
                        String note = line.substring(1).trim();
                        if (additionalNotes.length() > 0) {
                            additionalNotes.append("; ").append(note);
                        } else {
                            additionalNotes.append(note);
                        }
                    } else if (!line.startsWith("*")) {
                        // Handle regular text continuation
                        if (additionalNotes.length() > 0) {
                            additionalNotes.append(" ").append(line);
                        } else {
                            additionalNotes.append(line);
                        }
                    }
                }
            }

            // Build final test case object
            if (!id.isEmpty()) {
                testCase.put("id", id);
                testCase.put("title", title);
                testCase.put("description", description.toString().trim());

                // Clean up source - remove quotes if present
                String sourceStr = source.toString().trim();
                if (sourceStr.startsWith("\"") && sourceStr.endsWith("\"") && sourceStr.length() > 1) {
                    sourceStr = sourceStr.substring(1, sourceStr.length() - 1);
                }
                testCase.put("source", sourceStr);

                testCase.set("preconditions", preconditions);
                testCase.set("steps", steps);
                testCase.put("additionalNotes", additionalNotes.toString().trim());

                return testCase;
            }

            return null;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private void resetFlags() {
        // Helper method to reset all flags
    }
}