package com.stepup.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.dto.DataResponseDTO;
import com.stepup.dto.checklist.GenerateChecklistReqDTO;
import com.stepup.service.ChecklistService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping("/api/checklists")
@RequiredArgsConstructor
public class ChecklistController {

    private final ChecklistService checklistService;

    @PostMapping("/generate")
    public ResponseEntity<DataResponseDTO<?>> genCheckList(
            @RequestBody GenerateChecklistReqDTO generateChecklistReqDTO
    ) {
        try {
            JsonNode response = checklistService.getChecklist(null, generateChecklistReqDTO.getText(), generateChecklistReqDTO.getFeatureId());
            return ResponseEntity.ok(DataResponseDTO.success(response, "Checklist generated successfully"));
        } catch (IOException e) {
            return ResponseEntity.badRequest()
                    .body(DataResponseDTO.error("Error processing file: " + e.getMessage(), "FILE_PROCESSING_ERROR"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(DataResponseDTO.error("Error: " + e.getMessage(), "CHECKLIST_GENERATION_FAILED"));
        }
    }

    @PostMapping("/test-cases")
    public ResponseEntity<DataResponseDTO<?>> chatWithFile(
            @RequestParam("feature_id") String featureId
    ) {
        try {
            JsonNode response = checklistService.generateTestcase(featureId);
            return ResponseEntity.ok(DataResponseDTO.success(response, "Checklist generated successfully"));
        } catch (IOException e) {
            return ResponseEntity.badRequest()
                    .body(DataResponseDTO.error("Error processing file: " + e.getMessage(), "FILE_PROCESSING_ERROR"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(DataResponseDTO.error("Error: " + e.getMessage(), "CHECKLIST_GENERATION_FAILED"));
        }
    }
}