package com.stepup.controller;

import com.stepup.dto.DataResponseDTO;
import com.stepup.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"})
public class TestController {
    @Autowired
    private CommonService commonService;

    @GetMapping("/auth-status")
    public ResponseEntity<DataResponseDTO<Map<String, Object>>> getAuthStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("isAuthenticated", commonService.isUserAuthenticated());
        status.put("currentUserEmail", commonService.getCurrentUserEmail());
        status.put("currentUserId", commonService.getCurrentUserId());

        return ResponseEntity.ok(DataResponseDTO.success(status, "Authentication status retrieved"));
    }
} 