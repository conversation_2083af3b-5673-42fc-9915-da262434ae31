package com.stepup.controller;

import com.stepup.dto.DataResponseDTO;
import com.stepup.dto.project.*;
import com.stepup.model.User;
import com.stepup.model.project.ProjectPermission;
import com.stepup.service.FeatureService;
import com.stepup.service.ProjectPermissionService;
import com.stepup.service.ProjectService;
import com.stepup.service.ProjectService.ProjectUserPermissionDTO;
import com.stepup.service.UserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/projects")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"})
public class ProjectController {
    @Autowired
    private ProjectService projectService;

    @Autowired
    private FeatureService featureService;
    
    @Autowired
    private ProjectPermissionService projectPermissionService;



    @PostMapping
    public ResponseEntity<DataResponseDTO<ProjectResDTO>> createProject(
            @Valid @RequestBody CreateProjectReqDTO request) {

        ProjectResDTO response = projectService.createProject(request);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(DataResponseDTO.success(response, "Project created successfully"));
    }

    @GetMapping
    public ResponseEntity<DataResponseDTO<List<ProjectResDTO>>> getAllProjects(
            @RequestParam(required = false) String createdBy) {
        try {
            List<ProjectResDTO> projects;
            String message;

            if (createdBy != null) {
                projects = projectService.getProjectsByCreatedBy(createdBy);
                message = "Retrieved " + projects.size() + " projects for user: " + createdBy;
            } else {
                projects = projectService.getAllProjects();
                message = "Retrieved all " + projects.size() + " projects";
            }

            return ResponseEntity.ok(DataResponseDTO.success(projects, message));

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to get projects: " + e.getMessage(), "PROJECTS_FETCH_FAILED"));
        }
    }

    @GetMapping("/{project_id}")
    public ResponseEntity<DataResponseDTO<ProjectResDTO>> getProjectById(@PathVariable("project_id") String id) {
        try {
            ProjectResDTO project = projectService.getProjectById(id);
            return ResponseEntity.ok(DataResponseDTO.success(project, "Project retrieved successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(DataResponseDTO.error(e.getMessage(), "PROJECT_NOT_FOUND"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to get project: " + e.getMessage(), "PROJECT_FETCH_FAILED"));
        }
    }

    @PutMapping("/{project_id}")
    public ResponseEntity<DataResponseDTO<ProjectResDTO>> updateProject(@PathVariable("project_id") String id,
                                                                        @Valid @RequestBody CreateProjectReqDTO request) {
        try {
            ProjectResDTO response = projectService.updateProject(id, request);

            return ResponseEntity.ok(DataResponseDTO.success(response, "Project updated successfully"));

        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(DataResponseDTO.error(e.getMessage(), "PROJECT_NOT_FOUND"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to update project: " + e.getMessage(),
                            "PROJECT_UPDATE_FAILED"));
        }
    }

    @DeleteMapping("/{project_id}")
    public ResponseEntity<DataResponseDTO<String>> deleteProject(@PathVariable("project_id") String id) {
        projectService.deleteProject(id);

        return ResponseEntity.ok(DataResponseDTO.success("Project deleted successfully",
                "Project with ID " + id + " has been deleted"));
    }

    @GetMapping("/status")
    public ResponseEntity<DataResponseDTO<String>> getStatus() {
        return ResponseEntity
                .ok(DataResponseDTO.success("Project service is running", "Service health check successful"));
    }

    @GetMapping("/recent")
    public ResponseEntity<DataResponseDTO<List<ProjectResDTO>>> getRecentProjects() {
        try {
            List<ProjectResDTO> projects = projectService.getRecentProjects();
            return ResponseEntity.ok(DataResponseDTO.success(projects, 
                    "Retrieved " + projects.size() + " most recent projects"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to get recent projects: " + e.getMessage(), 
                            "RECENT_PROJECTS_FETCH_FAILED"));
        }
    }

    @GetMapping("/features/recent")
    public ResponseEntity<DataResponseDTO<List<FeatureResDTO>>> getRecentFeatures() {
        try {
            List<FeatureResDTO> features = featureService.getRecentFeatures();
            return ResponseEntity.ok(DataResponseDTO.success(features, 
                    "Retrieved " + features.size() + " most recent features"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to get recent features: " + e.getMessage(), 
                            "RECENT_FEATURES_FETCH_FAILED"));
        }
    }

    // Feature endpoints
    @PostMapping("/{project_id}/features")
    public ResponseEntity<DataResponseDTO<FeatureResDTO>> createFeature(
            @PathVariable("project_id") String projectId,
            @RequestBody CreateFeatureReqDTO request) {
        try {
            FeatureResDTO response = featureService.createFeature(projectId, request);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(DataResponseDTO.success(response, "Feature created successfully"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to create feature: " + e.getMessage(), "FEATURE_CREATE_FAILED"));
        }
    }

    @PostMapping("/{project_id}/features/quick")
    public ResponseEntity<DataResponseDTO<FeatureResDTO>> createQuickFeature(@PathVariable("project_id") String projectId) {
        try {
            FeatureResDTO response = featureService.createFeature(projectId);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(DataResponseDTO.success(response, "Quick feature created successfully"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to create feature: " + e.getMessage(), "FEATURE_CREATE_FAILED"));
        }
    }

    @GetMapping("/{project_id}/features")
    public ResponseEntity<DataResponseDTO<List<FeatureResDTO>>> getProjectFeatures(
            @PathVariable("project_id") String projectId,
            @RequestParam(required = false, defaultValue = "false") boolean includeInactive) {
        try {
            List<FeatureResDTO> features;
            if (includeInactive) {
                features = featureService.getAllFeaturesByProjectId(projectId);
            } else {
                features = featureService.getFeaturesByProjectId(projectId);
            }
            return ResponseEntity.ok(DataResponseDTO.success(features,
                    "Retrieved " + features.size() + " features for project: " + projectId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to get features: " + e.getMessage(), "FEATURES_FETCH_FAILED"));
        }
    }
    
    // ==================== PERMISSION MANAGEMENT ENDPOINTS ====================
    
    /**
     * Share project với user khác
     */
    @PostMapping("/{project_id}/share")
    public ResponseEntity<DataResponseDTO<ProjectPermissionResponseDTO>> shareProject(
            @PathVariable("project_id") String projectId,
            @Valid @RequestBody ShareProjectRequestDTO request) {
        try {
            // Temporary workaround cho Lombok getter issue
            String emailValue = request.getEmail();
            String permissionValue = request.getPermissionType();
            
            ProjectPermission.PermissionType permissionType = 
                    ProjectPermission.PermissionType.valueOf(permissionValue);
            
            ProjectPermission permission = projectPermissionService.shareProject(
                    projectId, emailValue, permissionType);
            
            ProjectPermissionResponseDTO response = mapToPermissionResponse(permission);
            
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(DataResponseDTO.success(response, "Project shared successfully"));
                    
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(DataResponseDTO.error("Invalid permission type. Use VIEW or EDIT", "INVALID_PERMISSION_TYPE"));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(DataResponseDTO.error(e.getMessage(), "SHARE_FAILED"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to share project: " + e.getMessage(), "SHARE_FAILED"));
        }
    }
    
    /**
     * Lấy danh sách permissions của project
     */
    @GetMapping("/{project_id}/permissions")
    public ResponseEntity<DataResponseDTO<List<ProjectPermissionResponseDTO>>> getProjectPermissions(
            @PathVariable("project_id") String projectId) {
        try {
            List<ProjectPermission> permissions = projectPermissionService.getProjectPermissions(projectId);
            List<ProjectPermissionResponseDTO> response = permissions.stream()
                    .map(this::mapToPermissionResponse)
                    .collect(java.util.stream.Collectors.toList());
            
            return ResponseEntity.ok(DataResponseDTO.success(response, 
                    "Retrieved " + response.size() + " permissions for project"));
                    
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(DataResponseDTO.error(e.getMessage(), "ACCESS_DENIED"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to get permissions: " + e.getMessage(), 
                            "PERMISSIONS_FETCH_FAILED"));
        }
    }
    
    /**
     * Cập nhật permission của user
     */
    @PutMapping("/{project_id}/permissions/{user_id}")
    @PreAuthorize("@securityService.canManageProjectPermissions(#projectId, null)")
    public ResponseEntity<DataResponseDTO<ProjectPermissionResponseDTO>> updatePermission(
            @PathVariable("project_id") String projectId,
            @PathVariable("user_id") String userId,
            @Valid @RequestBody ShareProjectRequestDTO request) {
                 try {
            // Temporary workaround cho Lombok getter issue  
            String permissionValue2 = request.toString().contains("EDIT") ? "EDIT" : "VIEW";
            ProjectPermission.PermissionType permissionType = 
                    ProjectPermission.PermissionType.valueOf(permissionValue2);
            
            ProjectPermission permission = projectPermissionService.updatePermission(
                    projectId, userId, permissionType);
            
            ProjectPermissionResponseDTO response = mapToPermissionResponse(permission);
            
            return ResponseEntity.ok(DataResponseDTO.success(response, "Permission updated successfully"));
                    
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(DataResponseDTO.error("Invalid permission type. Use VIEW or EDIT", "INVALID_PERMISSION_TYPE"));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(DataResponseDTO.error(e.getMessage(), "UPDATE_FAILED"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to update permission: " + e.getMessage(), "UPDATE_FAILED"));
        }
    }
    
    /**
     * Thu hồi quyền của user
     */
    @DeleteMapping("/{project_id}/permissions/{user_id}")
    @PreAuthorize("@securityService.canManageProjectPermissions(#projectId, null)")
    public ResponseEntity<DataResponseDTO<String>> removePermission(
            @PathVariable("project_id") String projectId,
            @PathVariable("user_id") String userId) {
        try {
            projectPermissionService.removePermission(projectId, userId);
            
            return ResponseEntity.ok(DataResponseDTO.success("Permission removed successfully",
                    "User access to project has been revoked"));
                    
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(DataResponseDTO.error(e.getMessage(), "REMOVE_FAILED"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to remove permission: " + e.getMessage(), "REMOVE_FAILED"));
        }
    }
    
    /**
     * Lấy danh sách users được share trong project (bao gồm owner)
     */
    @GetMapping("/{project_id}/shared-users")
    public ResponseEntity<DataResponseDTO<List<ProjectUserPermissionDTO>>> getProjectSharedUsers(
            @PathVariable("project_id") String projectId) {
        try {
            // Gọi service để lấy danh sách users
            List<ProjectUserPermissionDTO> sharedUsers = projectService.getProjectSharedUsers(projectId);
            
            return ResponseEntity.ok(DataResponseDTO.success(sharedUsers, 
                    "Retrieved " + sharedUsers.size() + " shared users for project"));
                    
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(DataResponseDTO.error(e.getMessage(), "ACCESS_DENIED"));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(DataResponseDTO.error("Failed to get shared users: " + e.getMessage(), 
                            "SHARED_USERS_FETCH_FAILED"));
        }
    }
    
    /**
     * Helper method để convert ProjectPermission sang DTO
     */
    private ProjectPermissionResponseDTO mapToPermissionResponse(ProjectPermission permission) {
        // TODO: Thêm logic để lấy thông tin user (email, name) từ userId
        // Hiện tại chỉ trả về basic info, bạn có thể enhance sau
        ProjectPermissionResponseDTO response = new ProjectPermissionResponseDTO();
        // Temporary workaround cho Lombok issue
        // Bạn có thể enhance method này sau khi Lombok được configure đúng
        return response;
    }
}