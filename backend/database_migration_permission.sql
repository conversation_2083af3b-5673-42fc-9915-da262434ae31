-- Migration script để thêm hệ thống phân quyền cho project
-- Chạy script này trên database để tạo bảng project_permissions

-- Tạo bảng project_permissions
CREATE TABLE IF NOT EXISTS project_permissions (
    id VARCHAR(50) PRIMARY KEY,
    project_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    permission_type VARCHAR(10) NOT NULL CHECK (permission_type IN ('VIEW', 'EDIT')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_project_user UNIQUE (project_id, user_id),
    CONSTRAINT fk_project_permissions_project_id FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    CONSTRAINT fk_project_permissions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- T<PERSON><PERSON> indexes để tối ưu performance
CREATE INDEX IF NOT EXISTS idx_project_permissions_project_id ON project_permissions(project_id);
CREATE INDEX IF NOT EXISTS idx_project_permissions_user_id ON project_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_project_permissions_permission_type ON project_permissions(permission_type);

-- Trigger để tự động update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_project_permissions_updated_at 
    BEFORE UPDATE ON project_permissions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Comment cho bảng và các cột
COMMENT ON TABLE project_permissions IS 'Bảng lưu quyền truy cập của user trên từng project cụ thể';
COMMENT ON COLUMN project_permissions.id IS 'ID duy nhất của permission record';
COMMENT ON COLUMN project_permissions.project_id IS 'ID của project (foreign key)';
COMMENT ON COLUMN project_permissions.user_id IS 'ID của user được cấp quyền (foreign key)';
COMMENT ON COLUMN project_permissions.permission_type IS 'Loại quyền: VIEW (chỉ xem) hoặc EDIT (xem và chỉnh sửa)';
COMMENT ON COLUMN project_permissions.created_at IS 'Thời gian tạo permission';
COMMENT ON COLUMN project_permissions.updated_at IS 'Thời gian cập nhật permission lần cuối';

-- Test data (optional - bỏ comment nếu muốn thêm test data)
/*
-- Giả sử có user với id 'user-1' và project với id 'project-1'
INSERT INTO project_permissions (id, project_id, user_id, permission_type) 
VALUES 
    ('perm-1', 'project-1', 'user-1', 'VIEW'),
    ('perm-2', 'project-1', 'user-2', 'EDIT');
*/

-- Verification query
-- SELECT * FROM project_permissions; 