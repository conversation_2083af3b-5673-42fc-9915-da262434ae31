# API: <PERSON><PERSON><PERSON> danh sách Users được Share trong Project

## Endpoint

```
GET /api/projects/{project_id}/shared-users
```

## Mô tả

API này trả về danh sách tất cả users có quyền truy cập vào project, bao gồm:
- Owner của project (quyền OWNER)
- Các users được share (quyền VIEW hoặc EDIT)

## Authorization

- User phải có ít nhất quyền **VIEW** trên project để xem danh sách
- Security được handle tự động bởi `@PreAuthorize` trong service layer

## Request

### URL Parameters
- `project_id` (string, required): ID của project cần lấy danh sách

### Headers
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

## Response

### Success Response (200 OK)

```json
{
  "success": true,
  "message": "Retrieved 3 shared users for project",
  "data": [
    {
      "userId": "user-123",
      "userEmail": "<EMAIL>",
      "userName": "Project Owner",
      "permissionType": "OWNER",
      "sharedAt": "2024-01-15T10:30:00"
    },
    {
      "userId": "user-456", 
      "userEmail": "<EMAIL>",
      "userName": "Editor User",
      "permissionType": "EDIT",
      "sharedAt": "2024-01-16T14:20:00"
    },
    {
      "userId": "user-789",
      "userEmail": "<EMAIL>", 
      "userName": "Viewer User",
      "permissionType": "VIEW",
      "sharedAt": "2024-01-17T09:15:00"
    }
  ],
  "error_code": null,
  "timestamp": "2024-01-18T12:00:00"
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `userId` | string | ID duy nhất của user |
| `userEmail` | string | Email của user |
| `userName` | string | Tên hiển thị của user |
| `permissionType` | string | Loại quyền: `OWNER`, `EDIT`, `VIEW` |
| `sharedAt` | datetime | Thời gian được share quyền (đối với owner là thời gian tạo project) |

### Permission Types

- **OWNER**: Chủ sở hữu project, có mọi quyền
- **EDIT**: Có thể xem và chỉnh sửa project, có thể share với users khác
- **VIEW**: Chỉ có thể xem project

## Error Responses

### 403 Forbidden
```json
{
  "success": false,
  "message": "Access denied: You don't have permission to view this project",
  "data": null,
  "error_code": "ACCESS_DENIED",
  "timestamp": "2024-01-18T12:00:00"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Project not found with id: project-123",
  "data": null,
  "error_code": "PROJECT_NOT_FOUND", 
  "timestamp": "2024-01-18T12:00:00"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to get shared users: Database connection error",
  "data": null,
  "error_code": "SHARED_USERS_FETCH_FAILED",
  "timestamp": "2024-01-18T12:00:00"
}
```

## Usage Examples

### cURL
```bash
curl -X GET "http://localhost:8080/api/projects/project-123/shared-users" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json"
```

### JavaScript (Fetch)
```javascript
const response = await fetch('/api/projects/project-123/shared-users', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
if (data.success) {
  console.log('Shared users:', data.data);
  data.data.forEach(user => {
    console.log(`${user.userName} (${user.userEmail}) - ${user.permissionType}`);
  });
}
```

### Java (Spring RestTemplate)
```java
@Autowired
private RestTemplate restTemplate;

HttpHeaders headers = new HttpHeaders();
headers.setBearerAuth(jwtToken);
HttpEntity<String> entity = new HttpEntity<>(headers);

ResponseEntity<DataResponseDTO<List<ProjectUserPermissionDTO>>> response = 
    restTemplate.exchange(
        "/api/projects/project-123/shared-users",
        HttpMethod.GET,
        entity,
        new ParameterizedTypeReference<DataResponseDTO<List<ProjectUserPermissionDTO>>>() {}
    );

List<ProjectUserPermissionDTO> sharedUsers = response.getBody().getData();
```

## Use Cases

### 1. Hiển thị danh sách trong Share Dialog
Sử dụng API này để hiển thị current sharing status khi user mở dialog "Share Project":

```javascript
// Khi mở share dialog
async function openShareDialog(projectId) {
  const sharedUsers = await getProjectSharedUsers(projectId);
  
  // Hiển thị owner
  const owner = sharedUsers.find(u => u.permissionType === 'OWNER');
  showOwnerInfo(owner);
  
  // Hiển thị danh sách shared users
  const editors = sharedUsers.filter(u => u.permissionType === 'EDIT');
  const viewers = sharedUsers.filter(u => u.permissionType === 'VIEW');
  
  renderSharedUsersList(editors, viewers);
}
```

### 2. Permission Management
Check ai có quyền gì để hiển thị appropriate UI:

```javascript
async function checkUserPermissions(projectId) {
  const sharedUsers = await getProjectSharedUsers(projectId);
  const currentUserEmail = getCurrentUserEmail();
  
  const currentUserPermission = sharedUsers.find(u => u.userEmail === currentUserEmail);
  
  if (currentUserPermission?.permissionType === 'OWNER' || 
      currentUserPermission?.permissionType === 'EDIT') {
    showShareButton();
    showEditOptions();
  } else {
    hideShareButton();
    showReadOnlyMode();
  }
}
```

## Implementation Notes

1. **Performance**: API này có thể được cache trong một thời gian ngắn (30s-1m) vì thông tin sharing không thay đổi thường xuyên

2. **Security**: Security được enforce tại service layer với `@PreAuthorize`, đảm bảo chỉ users có quyền mới có thể xem danh sách

3. **Sorting**: Danh sách được sắp xếp theo thứ tự:
   - Owner đầu tiên
   - Các EDIT users
   - Các VIEW users
   - Theo alphabet trong cùng permission level

4. **Future Enhancement**: 
   - Có thể thêm pagination nếu số lượng users lớn
   - Có thể thêm filter theo permission type
   - Có thể thêm search theo email/name 