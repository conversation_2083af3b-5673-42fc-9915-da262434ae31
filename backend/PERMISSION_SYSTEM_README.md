# Hệ Thống Phân Quyền Project - TestAngel

## Tổng Quan

Hệ thống phân quyền này cho phép chia sẻ quyền truy cập vào projects giữa các users với 2 mức độ quyền:
- **VIEW**: Chỉ xem được project
- **EDIT**: Xem và chỉnh sửa project

## Kiến Trúc

### 1. Database Schema

**Bảng `project_permissions`:**
```sql
project_permissions (
    id VARCHAR(50) PRIMARY KEY,
    project_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    permission_type VARCHAR(10) NOT NULL, -- 'VIEW' hoặc 'EDIT'
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    UNIQUE(project_id, user_id)
)
```

### 2. Models

- `ProjectPermission`: Entity để lưu quyền
- `ProjectPermission.PermissionType`: Enum với VIEW, EDIT

### 3. Core Components

- `ProjectPermissionService`: <PERSON> nghiệp vụ chính
- `SecurityService`: <PERSON><PERSON><PERSON> hợ<PERSON> với Spring Security
- `ProjectPermissionRepository`: Data access layer

## API Endpoints

### 1. Share Project
```
POST /api/projects/{project_id}/share
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "permission_type": "EDIT"
}
```

**Quyền yêu cầu:** Owner hoặc người có quyền EDIT

### 2. Xem Danh Sách Permissions
```
GET /api/projects/{project_id}/permissions
```

**Quyền yêu cầu:** Owner hoặc người có quyền EDIT

### 3. Cập Nhật Permission
```
PUT /api/projects/{project_id}/permissions/{user_id}
```

**Request Body:**
```json
{
  "permission_type": "VIEW"
}
```

### 4. Thu Hồi Quyền
```
DELETE /api/projects/{project_id}/permissions/{user_id}
```

## Security Integration

### @PreAuthorize Annotations

Các endpoints của project đã được bảo vệ:

```java
@GetMapping("/{project_id}")
@PreAuthorize("@securityService.canViewProject(#id)")
public ResponseEntity<ProjectResDTO> getProjectById(@PathVariable String id)

@PutMapping("/{project_id}")
@PreAuthorize("@securityService.canEditProject(#id)")
public ResponseEntity<ProjectResDTO> updateProject(@PathVariable String id, ...)
```

### Security Methods

- `canViewProject(projectId)`: Kiểm tra quyền VIEW
- `canEditProject(projectId)`: Kiểm tra quyền EDIT  
- `canManageProjectPermissions(projectId)`: Kiểm tra quyền quản lý permissions

## Logic Phân Quyền

### 1. Quyền Owner
- **Owner** của project (creator) có **toàn bộ quyền**
- Không thể xóa quyền của owner
- Owner có thể share với users khác

### 2. Quyền EDIT
- Có thể xem và chỉnh sửa project
- Có thể share project với users khác
- Có thể quản lý permissions

### 3. Quyền VIEW
- Chỉ có thể xem project
- Không thể chỉnh sửa
- Không thể share

### 4. Inheritance Logic
```
EDIT quyền bao gồm VIEW quyền
Owner có tất cả quyền
```

## Cài Đặt

### 1. Database Migration
```bash
# Chạy script SQL
psql -d testAngel -f database_migration_permission.sql
```

### 2. Configuration
Đảm bảo `@EnableMethodSecurity` được enable trong `SecurityConfig`:

```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {
    // ...
}
```

## Usage Examples

### 1. Share Project với quyền EDIT
```bash
curl -X POST http://localhost:8080/api/projects/{project_id}/share \
  -H "Authorization: Bearer {jwt_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "permission_type": "EDIT"
  }'
```

### 2. Xem danh sách permissions
```bash
curl -X GET http://localhost:8080/api/projects/{project_id}/permissions \
  -H "Authorization: Bearer {jwt_token}"
```

### 3. Thu hồi quyền
```bash
curl -X DELETE http://localhost:8080/api/projects/{project_id}/permissions/{user_id} \
  -H "Authorization: Bearer {jwt_token}"
```

## Testing

### Test Cases Cần Kiểm Tra

1. **Owner permissions:**
   - Owner có thể xem/sửa project
   - Owner có thể share với users khác
   - Không thể xóa quyền của owner

2. **EDIT permissions:**
   - User có EDIT có thể xem/sửa project
   - User có EDIT có thể share project
   - User có EDIT có thể quản lý permissions

3. **VIEW permissions:**
   - User có VIEW chỉ có thể xem
   - User có VIEW không thể sửa
   - User có VIEW không thể share

4. **Access Control:**
   - User không có permission không thể truy cập
   - API trả về 403 khi không có quyền

## Troubleshooting

### 1. Lombok Issues
Nếu gặp lỗi với getters/setters:
```bash
# Rebuild project
mvn clean compile
```

### 2. Permission Denied
- Kiểm tra JWT token có hợp lệ không
- Kiểm tra user có trong database không  
- Kiểm tra permissions trong bảng `project_permissions`

### 3. Database Errors
- Đảm bảo foreign keys tồn tại (project_id, user_id)
- Kiểm tra unique constraint (project_id, user_id)

## Roadmap

### Future Enhancements

1. **Invitation System**: Share với users chưa có tài khoản
2. **Role-based Permissions**: Thêm role ADMIN, MODERATOR
3. **Public Sharing**: Share project qua link public
4. **Permission Expiry**: Đặt thời hạn cho permissions
5. **Audit Log**: Ghi log các thao tác permissions

## Notes

- System hiện tại đơn giản và đáp ứng đúng yêu cầu "không overprogramming"
- Có thể dễ dàng nâng cấp lên Spring Security ACL nếu cần
- Database schema được thiết kế để dễ dàng mở rộng 