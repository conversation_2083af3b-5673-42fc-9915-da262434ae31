# Security tại Service Layer - Best Practices

## Tại sao sử dụng @PreAuthorize ở Service Layer?

### ✅ Lợi ích của việc đặt security tại Service Layer:

1. **Gần với Business Logic**: Security logic nằm gần với business logic thực tế
2. **T<PERSON>i sử dụng cao**: Nhiều controller có thể gọi cùng một service method  
3. **Bảo vệ tốt hơn**: Không thể bypass security qua các endpoint khác
4. **D<PERSON> bảo trì**: Centralized security logic, dễ dàng thay đổi logic phân quyền
5. **Testing tốt hơn**: Có thể test security logic độc lập với web layer

### ❌ Nhược điểm của Security tại Controller:

- <PERSON><PERSON> thể bypass qua việc gọi service trực tiếp
- Logic bị phân tán ở nhiều controller
- <PERSON><PERSON><PERSON> maintain khi có nhiều endpoint gọi cùng service
- <PERSON><PERSON><PERSON> k<PERSON> soát security khi có multiple entry points

## Triển Khai

### 1. <PERSON>ể<PERSON> từ Controller sang Service

**<PERSON><PERSON><PERSON><PERSON><PERSON> (Controller):**
```java
@RestController
public class ProjectController {
    
    @GetMapping("/{project_id}")
    @PreAuthorize("@securityService.canViewProject(#id)")
    public ResponseEntity<ProjectResDTO> getProject(@PathVariable String id) {
        ProjectResDTO project = projectService.getProjectById(id);
        return ResponseEntity.ok(project);
    }
}
```

**Sau (Service):**
```java
@Service
public class ProjectService extends CommonService {
    
    @PreAuthorize("@securityService.canViewProject(#id)")
    public ProjectResDTO getProjectById(String id) {
        Optional<Project> projectOpt = projectRepository.findById(id);
        if (projectOpt.isEmpty()) {
            throw new RuntimeException("Project not found with id: " + id);
        }
        return mapToProjectResponse(projectOpt.get());
    }
}

@RestController  
public class ProjectController {
    
    @GetMapping("/{project_id}")
    public ResponseEntity<ProjectResDTO> getProject(@PathVariable String id) {
        // Security được handle tự động tại service layer
        ProjectResDTO project = projectService.getProjectById(id);
        return ResponseEntity.ok(project);
    }
}
```

### 2. Các Security Annotations chính

```java
@Service
public class ProjectService extends CommonService {
    
    // Kiểm tra quyền VIEW (read)
    @PreAuthorize("@securityService.canViewProject(#projectId)")
    public ProjectResDTO getProjectById(String projectId) { ... }
    
    // Kiểm tra quyền EDIT (write)  
    @PreAuthorize("@securityService.canEditProject(#projectId)")
    public ProjectResDTO updateProject(String projectId, CreateProjectReqDTO request) { ... }
    
    // Kiểm tra quyền EDIT cho delete
    @PreAuthorize("@securityService.canEditProject(#projectId)")
    public void deleteProject(String projectId) { ... }
    
    // Kiểm tra quyền manage permissions
    @PreAuthorize("@securityService.canManageProjectPermissions(#projectId)")
    public List<ProjectPermission> getProjectPermissions(String projectId) { ... }
}
```

### 3. Permission Service với Security

```java
@Service
public class ProjectPermissionService extends CommonService {
    
    // Chỉ owner hoặc editor mới có thể share
    @PreAuthorize("@securityService.canManageProjectPermissions(#projectId)")
    @Transactional
    public ProjectPermission shareProject(String projectId, String userEmail, 
                                        ProjectPermission.PermissionType permissionType) {
        // Business logic
    }
    
    // Chỉ người có quyền quản lý mới xem được danh sách permissions
    @PreAuthorize("@securityService.canManageProjectPermissions(#projectId)")  
    public List<ProjectPermission> getProjectPermissions(String projectId) {
        // Business logic
    }
    
    // Cập nhật permission
    @PreAuthorize("@securityService.canManageProjectPermissions(#projectId)")
    @Transactional
    public ProjectPermission updatePermission(String projectId, String userId, 
                                            ProjectPermission.PermissionType newPermissionType) {
        // Business logic  
    }
    
    // Thu hồi quyền
    @PreAuthorize("@securityService.canManageProjectPermissions(#projectId)")
    @Transactional  
    public void removePermission(String projectId, String userId) {
        // Business logic
    }
}
```

## Security Service Implementation

```java
@Service("securityService")
public class SecurityService extends CommonService {
    
    @Autowired
    private ProjectPermissionService projectPermissionService;
    
    /**
     * Kiểm tra quyền VIEW - có thể xem project
     */
    public boolean canViewProject(String projectId) {
        String currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return projectPermissionService.hasPermission(projectId, currentUserId, 
                ProjectPermission.PermissionType.VIEW);
    }
    
    /**
     * Kiểm tra quyền EDIT - có thể chỉnh sửa project
     */
    public boolean canEditProject(String projectId) {
        String currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return projectPermissionService.hasPermission(projectId, currentUserId, 
                ProjectPermission.PermissionType.EDIT);
    }
    
    /**
     * Kiểm tra quyền quản lý permissions
     */
    public boolean canManageProjectPermissions(String projectId) {
        String currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            return false;
        }
        return projectPermissionService.canManagePermissions(projectId, currentUserId);
    }
}
```

## Method Level Security Configuration

Đảm bảo `@EnableMethodSecurity` được kích hoạt:

```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {
    // ... security configuration
}
```

## Exception Handling

Khi security check fail, Spring Security sẽ throw `AccessDeniedException`:

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<DataResponseDTO<String>> handleAccessDenied(AccessDeniedException ex) {
        return ResponseEntity.status(HttpStatus.FORBIDDEN)
                .body(DataResponseDTO.error("Access denied: " + ex.getMessage(), "ACCESS_DENIED"));
    }
}
```

## Testing Security at Service Layer

```java
@ExtendWith(MockitoExtension.class)
class ProjectServiceTest {
    
    @Mock
    private SecurityService securityService;
    
    @Mock  
    private ProjectRepository projectRepository;
    
    @InjectMocks
    private ProjectService projectService;
    
    @Test
    @WithMockUser(username = "<EMAIL>")
    void shouldAllowViewWhenUserHasPermission() {
        // Given
        when(securityService.canViewProject("project-1")).thenReturn(true);
        when(projectRepository.findById("project-1")).thenReturn(Optional.of(project));
        
        // When & Then
        assertDoesNotThrow(() -> projectService.getProjectById("project-1"));
    }
    
    @Test
    @WithMockUser(username = "<EMAIL>")  
    void shouldDenyViewWhenUserHasNoPermission() {
        // Given
        when(securityService.canViewProject("project-1")).thenReturn(false);
        
        // When & Then
        assertThrows(AccessDeniedException.class, 
                    () -> projectService.getProjectById("project-1"));
    }
}
```

## Best Practices

### 1. Consistency
- Luôn đặt security annotation ở service layer, không phải controller
- Sử dụng naming convention nhất quán cho security methods

### 2. Granular Permissions
```java
// Tốt: Specific permission cho từng action
@PreAuthorize("@securityService.canEditProject(#projectId)")
public void updateProject(String projectId, ...) { }

@PreAuthorize("@securityService.canDeleteProject(#projectId)") 
public void deleteProject(String projectId) { }

// Không tốt: Generic permission cho nhiều actions
@PreAuthorize("@securityService.hasProjectAccess(#projectId)")
public void updateProject(String projectId, ...) { }
```

### 3. Parameter Security
```java
// Đảm bảo parameter names match với SpEL expression
@PreAuthorize("@securityService.canViewProject(#projectId)")
public ProjectResDTO getProjectById(String projectId) { // ✅ parameter name match
    
// Không được:  
@PreAuthorize("@securityService.canViewProject(#projectId)")
public ProjectResDTO getProjectById(String id) { // ❌ parameter name không match
```

### 4. Transaction và Security
```java
@PreAuthorize("@securityService.canEditProject(#projectId)")
@Transactional  // Security check trước, transaction sau
public ProjectPermission shareProject(String projectId, ...) {
    // Business logic với transaction
}
```

## Troubleshooting

### 1. SpEL Expression Errors
- Đảm bảo parameter names trong method signature match với SpEL expression
- Kiểm tra bean name trong `@securityService`

### 2. Method Security không hoạt động
- Kiểm tra `@EnableMethodSecurity(prePostEnabled = true)`
- Đảm bảo class được Spring manage (có @Service annotation)
- Method phải là public

### 3. Circular Dependency
Nếu gặp circular dependency giữa SecurityService và BusinessService:
```java
// Giải pháp: Tách logic security thành service riêng
@Service
public class ProjectSecurityService {
    // Chỉ chứa logic kiểm tra quyền, không có business logic
}
```

## Migration Checklist

- [ ] Di chuyển tất cả `@PreAuthorize` từ Controller sang Service
- [ ] Xóa `@PreAuthorize` khỏi Controller methods 
- [ ] Cập nhật parameter names để match với SpEL expressions
- [ ] Test tất cả security scenarios
- [ ] Cập nhật documentation
- [ ] Review với team về security model mới 